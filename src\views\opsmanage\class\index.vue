<!-- 班级管理 -->
<template>
  <div class="class-management">
    <!-- 左侧菜单 -->
    <div class="class-management-left">
      <div class="left-header">
        <!-- {{ isClassOrGrade }}--{{ !changeScholl }} -->
        <a-select
          v-model:value="schoolId"
          placeholder="请选择学校"
          style="width: 100%"
          :options="options"
          @change="schoolChange"
          show-search
          :filter-option="filterOption"
          :not-found-content="null"
        ></a-select>
      </div>
      <div class="tree-container">
        <div v-if="treeData.length === 0 && changeScholl" class="empty-state">
          <div class="empty-icon">🏫</div>
          <div class="empty-text">请先选择学校</div>
          <div class="empty-desc">选择学校后将显示年级和班级信息</div>
        </div>
        <a-tree
          v-else
          :tree-data="treeData"
          :expanded-keys="expandedKeys"
          @expand="handleExpand"
          @select="select"
          class="custom-tree"
        >
          <template #title="{ title, key, levelId, learningOfficeName }">
            <div
              class="tree-node-title"
              :class="{ 'grade-node': levelId === 1, 'class-node': levelId === 2 }"
            >
              <span class="node-text">{{ title }}</span>
              <span v-if="levelId === 2 && learningOfficeName"
                >&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; {{ learningOfficeName }}</span
              >
            </div>
          </template>
        </a-tree>
        <a-button type="primary" style="margin-bottom: 10px" v-if="!changeScholl" @click="addGrade"
          >添加班级</a-button
        >
      </div>
    </div>
    <!-- 右侧列表 -->
    <div class="class-management-right">
      <!-- 班级列表 -->
      <GradeManageCp
        v-if="isClassOrGrade === 1"
        :searchParams="searchParams"
        :classDataProps="classDataProps"
        :showAddGrade="showAddGrade"
        @updateTree="updateTree"
      />
      <!-- 学生列表 -->
      <ClassManageCp
        v-else-if="isClassOrGrade === 2"
        :classesId="classesId"
        @updateTree="updateTree"
      />
      <!-- 空状态 -->
      <div v-else class="right-empty-state">
        <div class="empty-content">
          <div class="empty-icon">🎓</div>
          <div class="empty-text">班级管理系统</div>
          <div class="empty-desc">
            <p>请按照以下步骤开始使用：</p>
            <div class="steps">
              <div class="step">
                <span class="step-number">1</span>
                <span class="step-text">在左侧选择学校</span>
              </div>
              <div class="step">
                <span class="step-number">2</span>
                <span class="step-text">点击年级节点查看班级列表</span>
              </div>
              <div class="step">
                <span class="step-number">3</span>
                <span class="step-text">点击班级节点查看学生信息</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加班级弹窗 -->
    <a-modal
      v-model:open="showAddGrade"
      :title="'添加班级'"
      width="600px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="学校" name="schoolId">
          <a-select
            v-model:value="formData.schoolId"
            placeholder="请选择学校"
            style="width: 100%"
            show-search
            :disabled="modalTitle === '编辑班级'"
            :filter-option="filterOption"
          >
            <a-select-option
              v-for="school in options"
              :key="school.value"
              :value="school.value"
              :label="school.label"
            >
              {{ school.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="年级" name="grade">
          <a-select v-model:value="formData.grade" placeholder="请选择年级" style="width: 100%">
            <a-select-option
              v-for="grade in gradeOptions"
              :key="grade.value"
              :value="grade.value"
              :label="grade.label"
            >
              {{ grade.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="班级名称" name="className">
          <a-input
            v-model:value="formData.className"
            placeholder="请输入班级名称"
            :maxlength="50"
          />
        </a-form-item>

        <a-form-item label="班级类型" name="teachingLevel">
          <a-select
            v-model:value="formData.teachingLevel"
            placeholder="请选择班级类型"
            style="width: 100%"
          >
            <a-select-option :value="1">普通本科</a-select-option>
            <a-select-option :value="2">重点本科</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="ClassManage">
import ClassManageCp from "./components/class.vue"
import GradeManageCp from "./components/grade.vue"
import {
  getGradeListApi,
  getClassListApi,
  getSchoolDropdownListApi,
  getGradeDataApi,
  addClassListApi
} from "@/api/index.js"
import { onMounted, nextTick } from "vue"

/**
 * 学校数据
 */
const options = ref([])
/**
 * 选择的学校id
 */
const schoolId = ref(null)

/**
 * 学校选择器搜索过滤函数
 * @param {string} input 输入的搜索关键词
 * @param {object} option 选项对象
 * @returns {boolean} 是否匹配
 */
const filterOption = (input, option) => {
  // 支持按学校名称搜索（不区分大小写）
  return option.label.toLowerCase().includes(input.toLowerCase())
}
const changeScholl = ref(true)
/**
 *选择学校
 * @param schoolId 学校id
 */
const schoolChange = schoolId => {
  console.log("idaa", schoolId)
  changeScholl.value = false
  isClassOrGrade.value = 0
  expandedKeys.value = []
  //   获取年级数据
  getGradeListApi(schoolId).then(res => {
    if (res.code === 200) {
      treeData.value = res.data.map(i => {
        return {
          ...i,
          //   GradeString
          title: i.gradeDisplayName,
          key: i.gradeStr,
          isLeaf: false,
          // 自定义层级标识 1：第一级
          levelId: 1,
          children: []
        }
      })
      //   如果年级数据为空，则隐藏grade组件
      //   if (Array.isArray(treeData.value) && treeData.value.length === 0) {
      //     isClassOrGrade.value = 0
      //   }
    }
  })
}

// 表单验证规则
const rules = {
  schoolId: [{ required: true, message: "请选择学校", trigger: "change" }],
  grade: [{ required: true, message: "请选择年级", trigger: "change" }],
  className: [
    { required: true, message: "请输入班级名称", trigger: "blur" },
    { min: 1, max: 50, message: "班级名称长度为1-50个字符", trigger: "blur" }
  ],
  teachingLevel: [{ required: true, message: "请选择班级类型", trigger: "change" }]
}
const formRef = ref(null)
const submitLoading = ref(false)
const showAddGrade = ref(false)
const addGrade = () => {
  console.log("添加班级")
  showAddGrade.value = true
}
// 表单数据
const formData = ref({
  id: null,
  schoolId: null,
  grade: null,
  className: "",
  teachingLevel: null
})
// // 表单提交
// const handleSubmit = async () => {
//   await formRef.value.validate()
//   submitLoading.value = true
//   const reqdata = { ...formData.value }
//   addClassListApi(reqdata)
//     .then(res => {
//       if (res.code === 200) {
//         message.success("新增成功")
//         handleCancel()
//         // 更新树形数据
//         updateTree()
//         // 父组件会更新数据，不需要再次调用handleReset
//       } else {
//         message.error(res.message || "操作失败")
//       }
//     })
//     .catch(error => {
//       console.error("操作失败:", error)
//       // message.error("操作失败")
//     })
//     .finally(() => {
//       submitLoading.value = false
//     })
// }
// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    const reqdata = { ...formData.value }

    const res = await addClassListApi(reqdata)
    if (res.code === 200) {
      message.success("新增成功")
      handleCancel()
      // 更新树形数据
      await updateTree()
    } else {
      message.error(res.message || "操作失败")
    }
  } catch (error) {
    console.error("操作失败:", error)
  } finally {
    submitLoading.value = false
  }
}
const handleCancel = () => {
  console.log("取消")
  showAddGrade.value = false

  formData.value = {
    id: null,
    schoolId: null,
    grade: null,
    className: "",
    teachingLevel: null
  }
}
/**
 * tree数据(年级，班级)
 */
const treeData = ref([])
const expandedKeys = ref([])
/**
 * 0：空 1：点击年级 2：点击班级
 */
const isClassOrGrade = ref(0)
/**
 * 班级数据
 */
const classDataProps = ref([])
/**
 * 年级数据
 */
const gradeData = ref([])
/**
 * 点击的班级id
 */
const classesId = ref("")
// 班级列表查询条件
const searchParams = ref({})
/**
 * tree展开
 * @param keys
 * @param param1
 */
const handleExpand = (keys, { expanded, node }) => {
  // keys: 所有展开的key集合，expanded：当前节点展开状态
  console.log("keys", keys, expanded, node)
  expanded ? (expandedKeys.value = [node.key]) : (expandedKeys.value = [])
  if (expanded) {
    // 获取班级
    isClassOrGrade.value = 1
    searchParams.value = {
      SchoolId: schoolId.value,
      GradeLevel: node.dataRef.gradeLevel,
      GradeYear: node.dataRef.gradeYear
    }
    getClassListApi({
      SchoolId: schoolId.value,
      GradeLevel: node.dataRef.gradeLevel,
      GradeYear: node.dataRef.gradeYear,
      PageIndex: 1,
      PageSize: 9999
    }).then(res => {
      if (res.code === 200) {
        classDataProps.value = res.data.items
        const classData = res.data.items.map(i => {
          return {
            ...i,
            title: i.className,
            key: i.id,
            levelId: 2,
            learningOfficeName: i.learningOfficeName
          }
        })
        treeData.value.some(item => {
          if (item.key === node.key) {
            item.children = classData
            return true
          }
        })
      }
    })
  }
}
/**
 * 点击树节点
 */
const select = (keys, info) => {
  console.log("keys", keys, info)
  //   点击了年级
  if (info.node.levelId === 1) {
    console.log("年级")
    // 检查当前节点是否已展开
    const isExpanded = expandedKeys.value.includes(info.node.key)
    if (isExpanded) {
      // 如果已展开，则收起
      expandedKeys.value = []
    } else {
      // 如果未展开，则展开
      expandedKeys.value = [info.node.key]
      searchParams.value = {
        SchoolId: schoolId.value,
        GradeLevel: info.node.dataRef.gradeLevel,
        GradeYear: info.node.dataRef.gradeYear
      }
      // 获取班级数据
      getClassListApi({
        SchoolId: schoolId.value,
        GradeLevel: info.node.dataRef.gradeLevel,
        GradeYear: info.node.dataRef.gradeYear,
        PageIndex: 1,
        PageSize: 9999
      }).then(res => {
        if (res.code === 200) {
          isClassOrGrade.value = 1
          classDataProps.value = res.data.items
          const classData = res.data.items.map(i => {
            return {
              ...i,
              title: i.className,
              key: i.id,
              levelId: 2,
              learningOfficeName: i.learningOfficeName
            }
          })
          treeData.value.some(item => {
            if (item.key === info.node.key) {
              item.children = classData
              return true
            }
          })
        }
      })
    }
  } else if (info.node.levelId === 2) {
    //   点击了班级
    if (!keys[0]) {
      return
    }
    isClassOrGrade.value = 2
    classesId.value = keys[0]
  }
}

/**
 * 更新树形数据 - 子组件操作后刷新数据
 */
// const updateTree = async () => {
//   console.log("收到子组件updateTree事件，开始更新树形数据...")
//   // 如果当前有展开的节点，重新获取班级数据并更新树形结构
//   if (expandedKeys.value.length > 0 && searchParams.value.SchoolId) {
//     try {
//       console.log("更新参数:", searchParams.value)
//       const res = await getClassListApi({
//         ...searchParams.value,
//         PageIndex: 1,
//         PageSize: 9999
//       })

//       if (res.code === 200) {
//         console.log("获取到最新班级数据:", res.data.items)
//         // 更新右侧表格数据
//         classDataProps.value = res.data.items

//         // 构建新的班级节点数据
//         const classData = res.data.items.map(i => {
//           return {
//             ...i,
//             title: i.className,
//             key: i.id,
//             levelId: 2,
//             learningOfficeName: i.learningOfficeName // 确保包含学习官名称
//           }
//         })

//         // 使用nextTick确保响应式更新
//         await nextTick()

//         // 更新树形结构中当前展开节点的班级数据
//         const currentExpandedKey = expandedKeys.value[0]
//         const newTreeData = [...treeData.value]
//         const targetIndex = newTreeData.findIndex(item => item.key === currentExpandedKey)

//         if (targetIndex !== -1) {
//           // 只更新当前展开节点的children
//           newTreeData[targetIndex] = {
//             ...newTreeData[targetIndex],
//             children: [...classData]
//           }
//           treeData.value = newTreeData
//           console.log("树形数据更新成功")
//         }
//       }
//     } catch (error) {
//       console.error("更新树形数据失败:", error)
//     }
//   } else {
//     console.log("没有展开的节点或缺少学校信息，跳过更新")
//   }
// }
/**
 * 更新树形数据 - 子组件操作后刷新数据
 */
const updateTree = async () => {
  console.log("收到子组件updateTree事件，开始更新树形数据...")

  // 如果没有选择学校，直接返回
  if (!schoolId.value) {
    console.log("没有选择学校，跳过更新")
    return
  }

  try {
    // 保存当前展开的节点和选中状态
    const currentExpandedKey = expandedKeys.value[0]
    const currentIsClassOrGrade = isClassOrGrade.value
    const currentClassesId = classesId.value
    const currentSearchParams = { ...searchParams.value }

    // 重新获取年级数据
    const gradeRes = await getGradeListApi(schoolId.value)
    if (gradeRes.code === 200) {
      // 重新构建树形数据
      const newTreeData = gradeRes.data.map(i => {
        return {
          ...i,
          title: i.gradeDisplayName,
          key: i.gradeStr,
          isLeaf: false,
          levelId: 1,
          children: []
        }
      })

      // 如果之前有展开的节点，需要重新加载该节点的班级数据
      if (currentExpandedKey && currentSearchParams.SchoolId) {
        // 找到对应的年级节点
        const targetGradeIndex = newTreeData.findIndex(item => item.key === currentExpandedKey)

        if (targetGradeIndex !== -1) {
          const targetGrade = newTreeData[targetGradeIndex]

          // 重新获取该年级下的班级数据
          const classRes = await getClassListApi({
            SchoolId: currentSearchParams.SchoolId,
            GradeLevel: targetGrade.gradeLevel,
            GradeYear: targetGrade.gradeYear,
            PageIndex: 1,
            PageSize: 9999
          })

          if (classRes.code === 200) {
            // 构建班级节点数据
            const classData = classRes.data.items.map(i => {
              return {
                ...i,
                title: i.className,
                key: i.id,
                levelId: 2,
                learningOfficeName: i.learningOfficeName
              }
            })

            // 更新该年级节点的children
            newTreeData[targetGradeIndex].children = classData

            // 更新右侧表格数据
            classDataProps.value = classRes.data.items

            // 恢复展开状态
            expandedKeys.value = [currentExpandedKey]

            // 恢复右侧显示状态
            if (currentIsClassOrGrade === 1) {
              isClassOrGrade.value = 1
            } else if (currentIsClassOrGrade === 2) {
              // 如果之前选中的是班级，但现在班级列表为空，则显示班级列表
              if (classData.length === 0) {
                isClassOrGrade.value = 1
                classesId.value = ""
              } else {
                // 检查之前选中的班级是否还存在
                const classExists = classData.some(item => item.key === currentClassesId)
                if (classExists) {
                  isClassOrGrade.value = 2
                  classesId.value = currentClassesId
                } else {
                  // 如果之前选中的班级已被删除，则显示班级列表
                  isClassOrGrade.value = 1
                  classesId.value = ""
                }
              }
            }
          }
        }
      } else {
        // 如果之前没有展开节点，重置状态
        expandedKeys.value = []
        isClassOrGrade.value = 0
        classDataProps.value = []
        classesId.value = ""
      }

      // 更新树形数据
      treeData.value = newTreeData
      console.log("树形数据完全更新成功")
    }
  } catch (error) {
    console.error("更新树形数据失败:", error)
  }
}
// 假数据 - 年级选项
const gradeOptions = ref([])
onMounted(() => {
  getSchoolDropdownListApi({
    PageIndex: 1,
    PageSize: 9999
  }).then(res => {
    if (res.code === 200) {
      options.value = res.data.items.map(item => {
        return {
          label: item.name,
          value: item.id
        }
      })
    }
  })

  // 获取年级
  getGradeDataApi().then(res => {
    if (res.code === 200) {
      gradeOptions.value = res.data.map(item => {
        return {
          label: item.text,
          value: item.text
        }
      })
    }
  })
})
</script>

<style lang="scss" scoped>
.class-management {
  display: flex;
  gap: 20px;
  height: calc(100vh - 180px); // 设置固定高度，减去顶部导航等高度
  overflow: hidden;
  .class-management-left {
    width: 280px;
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;

    .left-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
      border-radius: 8px 8px 0 0;
      flex-shrink: 0;
    }

    .tree-container {
      flex: 1; // 填充剩余空间
      overflow-y: auto; // 启用垂直滚动
      overflow-x: hidden; // 隐藏水平滚动
      padding: 8px;
      display: flex;
      flex-direction: column;

      // 空状态样式
      .empty-state {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 40px 20px;

        .empty-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.6;
        }

        .empty-text {
          font-size: 16px;
          color: #666;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .empty-desc {
          font-size: 14px;
          color: #999;
          line-height: 1.4;
        }
      }

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }

      .custom-tree {
        // 重置tree组件的默认样式
        :deep(.ant-tree-treenode) {
          padding: 2px 0;

          .ant-tree-node-content-wrapper {
            border-radius: 6px;
            transition: all 0.2s ease;
            padding: 6px 8px;
            margin: 2px 0;

            &:hover {
              background-color: #f5f5f5;
            }

            &.ant-tree-node-selected {
              background-color: #e6f7ff;
              border: 1px solid #91d5ff;
            }
          }

          .ant-tree-switcher {
            width: 20px;
            height: 20px;
            line-height: 20px;
            margin-top: 6px;

            .ant-tree-switcher-icon {
              font-size: 12px;
              color: #666;
            }
          }

          .ant-tree-title {
            font-size: 14px;
          }
        }

        // 年级节点样式
        :deep(.grade-node) {
          .node-text {
            font-weight: 600;
            color: #1890ff;
            font-size: 14px;
          }
        }

        // 班级节点样式
        :deep(.class-node) {
          .node-text {
            color: #666;
            font-size: 13px;
            padding-left: 8px;
            position: relative;

            &::before {
              content: "•";
              color: #52c41a;
              font-weight: bold;
              position: absolute;
              left: -4px;
            }
          }
        }

        // 连接线样式
        :deep(.ant-tree-indent-unit) {
          width: 16px;
        }

        :deep(.ant-tree-treenode-switcher-open) {
          .ant-tree-switcher .ant-tree-switcher-icon {
            color: #1890ff;
          }
        }
      }
    }
  }

  .class-management-right {
    flex: 1;
    background: #fff;
    border-radius: 8px;
    overflow-y: auto;

    .right-empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;

      .empty-content {
        text-align: center;
        max-width: 400px;

        .empty-icon {
          font-size: 64px;
          margin-bottom: 24px;
          opacity: 0.8;
        }

        .empty-text {
          font-size: 24px;
          font-weight: 600;
          color: #1890ff;
          margin-bottom: 16px;
        }

        .empty-desc {
          color: #666;
          line-height: 1.6;

          p {
            font-size: 16px;
            margin-bottom: 20px;
          }

          .steps {
            text-align: left;

            .step {
              display: flex;
              align-items: center;
              margin-bottom: 12px;
              padding: 8px 16px;
              background: #f5f5f5;
              border-radius: 6px;
              transition: all 0.3s ease;

              &:hover {
                background: #e6f7ff;
                transform: translateX(4px);
              }

              .step-number {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                background: #1890ff;
                color: white;
                border-radius: 50%;
                font-size: 12px;
                font-weight: 600;
                margin-right: 12px;
                flex-shrink: 0;
              }

              .step-text {
                font-size: 14px;
                color: #333;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .class-management {
    .class-management-left {
      width: 240px;
    }
  }
}

@media (max-width: 768px) {
  .class-management {
    flex-direction: column;

    .class-management-left {
      width: 100%;
      min-height: 400px; // 小屏幕上的最小高度
      margin-bottom: 16px;

      .tree-container {
        max-height: none; // 小屏幕上移除最大高度限制，让它完全填充
      }
    }
  }
}
</style>
