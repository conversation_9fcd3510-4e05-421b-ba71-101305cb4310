<template>
  <div class="learnoffice-management">
    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false" style="margin-bottom: 16px">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="姓名" class="marginBot">
          <a-input
            v-model:value="searchForm.Name"
            placeholder="请输入姓名"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="账号" class="marginBot">
          <a-input
            v-model:value="searchForm.Account"
            placeholder="请输入账号"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="启用状态" class="marginBot">
          <a-select
            v-model:value="searchForm.Enable"
            placeholder="请选择启用状态"
            style="width: 200px"
            allow-clear
          >
            <a-select-option :value="true">启用</a-select-option>
            <a-select-option :value="false">禁用</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="marginBot">
          <a-button type="primary" html-type="submit" :loading="searchLoading"> 搜索 </a-button>
          <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <!-- 操作按钮区域 -->
    <a-card style="margin-bottom: 16px">
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </div>
    </a-card>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="learnofficeData"
      :pagination="pagination"
      row-key="id"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'state'">
          <a-tag :color="record.state === 1 ? 'green' : 'red'">
            {{ record.state === 1 ? "启用" : "禁用" }}
          </a-tag>
        </template>
        <template v-if="column.key === 'isTeamLeader'">
          <a-tag :color="record.isTeamLeader ? 'green' : 'default'">
            {{ record.isTeamLeader ? "是" : "否" }}
          </a-tag>
        </template>
        <template v-if="column.key === 'imImport'">
          <a-tag v-if="record.imImport" color="success" style="font-size: 12px">已导入</a-tag>
          <div v-else>
            <a-tag color="#ccc" style="font-size: 12px; margin-bottom: 5px">未导入</a-tag>
            <a-button type="primary" style="font-size: 12px" size="small" @click="importFn(record)"
              >导入</a-button
            >
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleDetail(record)">详情</a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" size="small" @click="handleEditPwd(record)">修改密码</a-button>
            <a-popconfirm
              title="确定删除吗?"
              ok-text="是"
              cancel-text="否"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="所属云校" name="cloudSchoolId">
          <a-select
            v-model:value="formData.cloudSchoolId"
            placeholder="请选择所属云校"
            :loading="cloudSchoolLoading"
            :filter-option="false"
          >
            <a-select-option v-for="item in cloudSchoolOptions" :key="item.id" :value="item.id">
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="姓名" name="realName">
          <a-input v-model:value="formData.realName" placeholder="请输入姓名" />
        </a-form-item>

        <a-form-item label="账号" name="account">
          <a-input
            v-model:value="formData.account"
            placeholder="请输入账号"
            :disabled="!!formData.id"
          />
        </a-form-item>

        <a-form-item label="手机号" name="phone">
          <a-input v-model:value="formData.phone" placeholder="请输入手机号" />
        </a-form-item>

        <!-- 新增时显示密码输入框 -->
        <a-form-item v-if="!formData.id" label="密码" name="password">
          <a-input-password
            v-model:value="formData.password"
            placeholder="请输入密码"
            :maxlength="20"
          />
        </a-form-item>

        <a-form-item label="启用状态" name="state">
          <a-radio-group v-model:value="formData.state">
            <a-radio :value="0">禁用</a-radio>
            <a-radio :value="1">启用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="服务班级" name="followClassId">
          <div style="display: flex; align-items: center">
            <a-input
              :value="getDisplayClassNames(formData.followClassId)"
              size="small"
              disabled
              placeholder="请选择服务班级"
            />
            <a-button type="link" size="small" @click="handleClassSelect('follow')">
              选择服务班级
            </a-button>
          </div>
        </a-form-item>

        <a-form-item label="是否组长" name="isTeamLeader">
          <a-radio-group v-model:value="formData.isTeamLeader">
            <a-radio :value="false">否</a-radio>
            <a-radio :value="true">是</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item v-if="formData.isTeamLeader" label="管理班级" name="manageClassId">
          <div style="display: flex; align-items: center">
            <a-input
              :value="getDisplayClassNames(formData.manageClassId)"
              size="small"
              disabled
              placeholder="请选择管理班级"
            />
            <a-button type="link" size="small" @click="handleClassSelect('manage')">
              选择管理班级
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 班级选择弹窗 -->
    <a-modal
      v-model:open="classSelectVisible"
      :title="classSelectTitle"
      @ok="handleClassSelectConfirm"
      width="800px"
      @cancel="handleClassSelectCancel"
    >
      <div class="class-select-container" style="display: flex; gap: 20px">
        <!-- 左侧选择区域 -->
        <div class="left-panel" style="flex: 2">
          <!-- 学校选择下拉框 -->
          <div class="school-select" style="background-color: #fff">
            <a-cascader
              v-model:value="cascaderValue"
              :options="cascaderOptions"
              placeholder="请选择学校 > 年级（支持搜索）"
              style="margin-bottom: 16px; width: 100%"
              :loading="schoolLoading"
              :load-data="loadCascaderData"
              change-on-select
              :allow-clear="false"
              :show-search="true"
              @change="handleCascaderChange"
            />
          </div>

          <!-- 班级列表表格 -->
          <div class="class-table" style="width: 480px">
            <a-table
              :columns="classColumns"
              :data-source="classList"
              :pagination="false"
              :loading="classLoading"
              row-key="id"
              size="small"
              :scroll="{ y: 300 }"
              :row-selection="{
                selectedRowKeys: selectedClassKeys,
                onChange: onClassSelectChange,
                getCheckboxProps: record => ({
                  // 存在学习官或者组长的班级不能选择
                  disabled: isDisabled(record)
                })
              }"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'status' && currentClassSelectType === 'manage'">
                  <a-tag v-if="isClassSelectedAsFollow(record)" color="orange" size="small">
                    已选为服务班级
                  </a-tag>
                  <a-tag v-else-if="record.learningOfficeLeaderId > 0" color="red" size="small">
                    已有组长
                  </a-tag>
                </template>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 右侧已选择区域 -->
        <div
          class="right-panel"
          style="flex: 1; max-height: 400px; overflow-y: auto; padding: 10px; background: #f8f8f8"
        >
          <!-- <div class="selected-title">已选择班级</div> -->
          <div class="selected-list" v-if="selectedClassList.length > 0">
            <div
              v-for="item in selectedClassList"
              :key="item.id"
              class="selected-item"
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                margin-bottom: 7px;
              "
            >
              <span>{{ item.className }}</span>
              <a-button type="text" danger size="small" @click="removeSelectedClass(item)">
                删除
              </a-button>
            </div>
          </div>
          <div v-else>
            <a-empty description="" style="padding-top: 90px" />
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 修改密码弹窗 -->
    <a-modal
      v-model:open="passwordModalVisible"
      title="修改密码"
      width="400px"
      :confirm-loading="passwordSubmitLoading"
      @ok="handlePasswordSubmit"
      @cancel="handlePasswordCancel"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordFormData"
        :rules="passwordRules"
        layout="vertical"
      >
        <a-form-item label="新密码" name="newPassword">
          <a-input-password
            v-model:value="passwordFormData.newPassword"
            placeholder="请输入新密码"
            :maxlength="20"
          />
        </a-form-item>

        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="passwordFormData.confirmPassword"
            placeholder="请再次输入新密码"
            :maxlength="20"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="学习官详情"
      width="600px"
      :footer="null"
      @cancel="handleDetailCancel"
    >
      <a-spin :spinning="detailLoading">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="所属云校">
            {{ detailData.cloudSchoolName || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="姓名">
            {{ detailData.realName || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="账号">
            {{ detailData.account || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="手机号">
            {{ detailData.phone || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="detailData.state === 1 ? 'green' : 'red'">
              {{ detailData.state === 1 ? "启用" : "禁用" }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="是否组长">
            <a-tag :color="detailData.isTeamLeader ? 'green' : 'default'">
              {{ detailData.isTeamLeader ? "是" : "否" }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="服务班级">
            <div v-if="detailData.followClassList?.length > 0">
              <a-tag color="blue" v-for="item in detailData.followClassList" :key="item.id">
                {{ item.className }}
              </a-tag>
            </div>
            <div v-else>-</div>
          </a-descriptions-item>
          <a-descriptions-item label="管理班级">
            <div v-if="detailData.manageClassList?.length > 0">
              <a-tag color="blue" v-for="item in detailData.manageClassList" :key="item.id">
                {{ item.className }}
              </a-tag>
            </div>
            <div v-else>-</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-spin>
    </a-modal>

    <!-- 导入IM弹窗 -->
    <a-modal
      v-model:open="importModalVisible"
      title="导入IM"
      width="400px"
      :confirm-loading="importSubmitLoading"
      @ok="handleImportSubmit"
      @cancel="handleImportCancel"
    >
      <a-form ref="importFormRef" :model="importFormData" layout="vertical">
        <a-form-item label="昵称" name="nick" required>
          <a-input v-model:value="importFormData.nick" placeholder="请输入昵称" maxlength="20" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="LearnOffice">
import { ref, reactive, onMounted, nextTick, computed, watch } from "vue"
import { message } from "ant-design-vue"
import {
  getLoListApi,
  getLoDetailApi,
  getCloudSchoolDropdownListApi,
  updateLoPwdApi,
  getSchoolDropdownListApi,
  getClassListApi,
  getGradeListApi,
  addLoApi,
  updateLoApi,
  deleteLoApi,
  importLoImApi,
  getManageClassListApi
} from "@/api"
// 表格列配置
const columns = [
  {
    title: "id",
    dataIndex: "id",
    key: "id"
  },
  {
    title: "姓名",
    dataIndex: "realName",
    key: "realName"
  },
  {
    title: "账号",
    dataIndex: "account",
    key: "account"
  },
  {
    title: "手机号",
    dataIndex: "phone",
    key: "phone"
  },
  {
    title: "所属云校",
    dataIndex: "cloudSchoolName",
    key: "cloudSchoolName"
  },
  {
    title: "状态",
    dataIndex: "state",
    key: "state"
  },
  {
    title: "是否组长",
    dataIndex: "isTeamLeader",
    key: "isTeamLeader"
  },
  {
    title: "IM状态",
    dataIndex: "imImport",
    key: "imImport"
  },
  {
    title: "服务班级数",
    dataIndex: "followClassCount",
    key: "followClassCount"
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right"
  }
]
const isDisabled = record => {
  // 检查当前班级是否已被选为服务班级（在选择管理班级时禁用）
  if (currentClassSelectType.value === "manage") {
    const currentFollowClasses = formData.value.followClassId || []
    const isSelectedAsFollow = currentFollowClasses.some(
      followClass => followClass.id === record.id
    )
    if (isSelectedAsFollow) {
      return true // 已被选为服务班级，在管理班级中禁用
    }
  }

  // 新增的时候
  if (modalTitle.value === "新增学习官") {
    if (currentClassSelectType.value === "follow") {
      return record.learningOfficeId > 0
    } else {
      return record.learningOfficeLeaderId > 0
    }
  } else {
    // 编辑的时候
    if (record.id) {
      if (currentClassSelectType.value === "follow") {
        // 这里需要从本地储存中获取数据，因为编辑时服务班级和管理班级回显后可能会被移除，此时如果用表单数据就会导致弹窗中被移除的回显数据无法再次被选中
        const followClassId = JSON.parse(localStorage.getItem("followClassId"))
        const hasSelect = followClassId.find(item => item.id === record.id)
        // 如果当前编辑的学习官在表格中出现，则不禁用
        if (hasSelect) {
          console.log(hasSelect)
          return false
        }
      } else {
        const manageClassId = JSON.parse(localStorage.getItem("manageClassId"))
        console.log("33333", manageClassId)

        const hasSelect = manageClassId.find(item => item.id === record.id)
        // 如果当前编辑的学习官在表格中出现，则不禁用
        if (hasSelect) {
          return false
        }
      }
    }
    if (currentClassSelectType.value === "follow") {
      return record.learningOfficeId > 0
    } else {
      return record.learningOfficeLeaderId > 0
    }
  }
}

// 检查班级是否已被选为服务班级
const isClassSelectedAsFollow = record => {
  const currentFollowClasses = formData.value.followClassId || []
  return currentFollowClasses.some(followClass => followClass.id === record.id)
}

// getCloudSchoolDropdownListApi()
// 响应式数据
const loading = ref(false)
// 表格查询参数
const searchForm = ref({
  Name: undefined,
  Account: undefined,
  Enable: undefined
})
const searchLoading = ref(false)
/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  getLearnofficeList()
}
// 重置表格数据
const handleReset = () => {
  searchForm.value = {
    Name: undefined,
    Account: undefined,
    Enable: undefined
  }
  pagination.current = 1
  pagination.pageSize = 10
  getLearnofficeList()
}
// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    getLearnofficeList()
  },
  onShowSizeChange: (_, size) => {
    pagination.current = 1
    pagination.pageSize = size
    getLearnofficeList()
  }
})

// 学习官数据
const learnofficeData = ref([])

/**
 * 获取学习官列表数据
 */
const getLearnofficeList = async () => {
  // 如果正在加载中，直接返回，避免重复调用
  if (loading.value) {
    console.log("正在加载中，跳过重复调用")
    return
  }

  try {
    loading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize,
      ...searchForm.value
    }

    console.log("调用学习官列表接口，参数:", params)
    const res = await getLoListApi(params)
    console.log("学习官列表数据:", res)

    if (res && res.code === 200) {
      learnofficeData.value = res.data.items || []
      pagination.total = res.data.total || 0
      console.log("学习官列表数据加载完成，条数:", learnofficeData.value.length)
    } else {
      message.error(res?.msg || "获取学习官列表失败")
      learnofficeData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error("获取学习官列表失败:", error)
    learnofficeData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 新增学习官
const handleAdd = () => {
  modalTitle.value = "新增学习官"
  modalVisible.value = true
  formData.value = {
    id: null,
    cloudSchoolId: null,
    realName: "",
    account: "",
    phone: "",
    password: "",
    state: 1,
    isTeamLeader: false,
    followClassId: [],
    manageClassId: []
  }
  // 保存初始学习官服务班级数据和管理班级数据
  localStorage.setItem("followClassId", JSON.stringify([]))
  localStorage.setItem("manageClassId", JSON.stringify([]))
  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 查看详情
const handleDetail = async record => {
  try {
    detailLoading.value = true
    const res = await getLoDetailApi(record.id)
    if (res && res.code === 200) {
      detailData.value = res.data
      detailVisible.value = true
    } else {
      message.error(res?.msg || "获取详情失败")
    }
  } finally {
    detailLoading.value = false
  }
}

// 编辑学习官
const handleEdit = async record => {
  modalTitle.value = "编辑学习官"
  modalVisible.value = true
  getLoDetailApi(record.id).then(res => {
    if (res && res.code === 200) {
      const Data = res.data
      // formData.value = Data
      formData.value = {
        id: Data.id,
        cloudSchoolId: Data.cloudSchoolId || null,
        realName: Data.realName,
        account: Data.account,
        phone: Data.phone,
        state: Data.state,
        isTeamLeader: Data.isTeamLeader,
        followClassId: Data.followClassList || [],
        manageClassId: Data.manageClassList || []
      }
      // 保存初始学习官服务班级数据和管理班级数据
      localStorage.setItem("followClassId", JSON.stringify(Data.followClassList || []))
      localStorage.setItem("manageClassId", JSON.stringify(Data.manageClassList || []))
    }
  })

  // 如果有云校ID，需要设置对应的选项
  // if (record.cloudSchoolId && record.cloudSchoolName) {
  //   cloudSchoolOptions.value = [
  //     {
  //       id: record.cloudSchoolId,
  //       name: record.cloudSchoolName
  //     }
  //   ]
  // }

  // 重置表单验证
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 修改密码
const handleEditPwd = record => {
  console.log("修改密码:", record)
  passwordModalVisible.value = true
  currentPasswordRecord.value = record
  // const reqParams = {
  //   id: record.id,
  //   newPassword: ""
  // }
  // 重置密码表单数据
  Object.assign(passwordFormData, {
    newPassword: "",
    confirmPassword: ""
  })
}

// 删除学习官
const handleDelete = record => {
  console.log("删除学习官:", record)
  deleteLoApi(record.id).then(res => {
    if (res && res.code === 200) {
      message.success(`删除学习官: ${record.realName}`)
      // 刷新列表
      getLearnofficeList()
    } else {
      message.error(res?.msg || "删除失败")
    }
  })
}

// 详情弹窗相关
const detailVisible = ref(false)
const detailLoading = ref(false)
const detailData = ref({})

const handleDetailCancel = () => {
  detailVisible.value = false
  detailData.value = {}
}

// 新增/编辑弹窗相关
const modalVisible = ref(false)
const modalTitle = ref("新增学习官")
const submitLoading = ref(false)
const formRef = ref(null)
const formData = ref({})

// 表单验证规则
const rules = ref({
  cloudSchoolId: [{ required: true, message: "请选择所属云校", trigger: "change" }],
  realName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  account: [{ required: true, message: "请输入账号", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: /^[0-9]{11}$/, message: "请输入11位数字", trigger: "blur" }
  ],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
  state: [{ required: true, message: "请选择启用状态", trigger: "change" }],
  isTeamLeader: [{ required: true, message: "请选择是否组长", trigger: "change" }],
  followClassId: [{ required: true, message: "请选择服务班级", trigger: "blur" }],
  manageClassId: [{ required: true, message: "请选择管理班级", trigger: "blur" }]
})

const cloudSchoolLoading = ref(false)
const cloudSchoolOptions = ref([])
watch(
  () => modalVisible.value,
  val => {
    val ? handleCloudSchoolSearch() : ""
  }
)

const handleCloudSchoolSearch = async () => {
  try {
    cloudSchoolLoading.value = true
    const res = await getCloudSchoolDropdownListApi()
    if (res && res.code === 200) {
      cloudSchoolOptions.value = res.data
    } else {
      message.error(res?.msg || "获取云校列表失败")
      cloudSchoolOptions.value = []
    }
  } finally {
    cloudSchoolLoading.value = false
  }
}

const handleSubmit = async () => {
  // 防止重复提交
  if (submitLoading.value) {
    console.log("正在提交中，请勿重复点击")
    return
  }

  try {
    await formRef.value.validate()
    submitLoading.value = true

    const reqParams = {
      ...formData.value,
      followClassId: formData.value.followClassId.map(i => i.id),
      manageClassId: formData.value.manageClassId.map(i => i.id)
    }
    console.log("请求参数:", reqParams)

    let res
    if (formData.value.id) {
      // 编辑学习官
      res = await updateLoApi(reqParams)
    } else {
      // 新增学习官
      res = await addLoApi(reqParams)
    }

    if (res && res.code === 200) {
      message.success(formData.value.id ? "编辑成功" : "新增成功")
      modalVisible.value = false
      // 刷新列表
      getLearnofficeList()
    } else {
      message.error(res?.msg || "操作失败")
    }
  } catch (error) {
    console.log("表单验证失败:", error)
    message.error("操作失败，请检查网络连接")
  } finally {
    submitLoading.value = false
  }
}

const handleCancel = () => {
  modalVisible.value = false
  submitLoading.value = false // 重置提交loading状态
  formData.value = {}
  cloudSchoolOptions.value = []
  // 重置表单
  nextTick(() => {
    formRef.value?.resetFields()
  })
}

// 组件挂载时获取数据
onMounted(() => {
  console.log("学习官管理组件已加载")
  getLearnofficeList()
  // 初始化云校选项
  initCloudSchoolOptions()
})

// 初始化云校选项
const initCloudSchoolOptions = async () => {
  try {
    const res = await getCloudSchoolDropdownListApi()
    if (res && res.code === 200) {
      cloudSchoolOptions.value = res.data
    }
  } catch (error) {
    console.error("获取云校选项失败:", error)
  }
}

// 修改密码相关数据
const passwordModalVisible = ref(false)
const passwordSubmitLoading = ref(false)
const passwordFormRef = ref()
const currentPasswordRecord = ref(null)

// 修改密码表单数据
const passwordFormData = reactive({
  newPassword: "",
  confirmPassword: ""
})

// 修改密码表单验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度为6-20个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (_, value) => {
        if (value && value !== passwordFormData.newPassword) {
          return Promise.reject("两次输入的密码不一致")
        }
        return Promise.resolve()
      },
      trigger: "blur"
    }
  ]
}

const handlePasswordSubmit = async () => {
  try {
    await passwordFormRef.value.validate()
    passwordSubmitLoading.value = true
    const submitData = {
      id: currentPasswordRecord.value.id,
      newPassword: passwordFormData.newPassword
    }
    const res = await updateLoPwdApi(submitData)
    if (res && res.code === 200) {
      message.success("密码修改成功")
      passwordModalVisible.value = false
      // 重置表单数据
      Object.assign(passwordFormData, {
        newPassword: "",
        confirmPassword: ""
      })
      currentPasswordRecord.value = null
    } else {
      message.error(res?.msg || "密码修改失败")
    }
  } catch (error) {
    console.error("密码修改失败:", error)
  } finally {
    passwordSubmitLoading.value = false
  }
}

const handlePasswordCancel = () => {
  passwordModalVisible.value = false
  passwordFormRef.value?.resetFields()
  // 重置表单数据
  Object.assign(passwordFormData, {
    newPassword: "",
    confirmPassword: ""
  })
  currentPasswordRecord.value = null
}

// 新增班级选择逻辑
const handleClassSelect = type => {
  classSelectTitle.value = type === "follow" ? "选择服务班级" : "选择管理班级"
  classSelectVisible.value = true
  currentClassSelectType.value = type

  // 重置选择状态，但保留已选择的班级列表
  cascaderValue.value = []
  selectedClassKeys.value = []
  classList.value = []

  // 根据当前表单数据设置弹窗已选择的班级
  if (type === "follow" && formData.value.followClassId) {
    selectedClassList.value = Array.isArray(formData.value.followClassId)
      ? formData.value.followClassId
      : []
  } else if (type === "manage" && formData.value.manageClassId) {
    selectedClassList.value = Array.isArray(formData.value.manageClassId)
      ? formData.value.manageClassId
      : []
  } else {
    selectedClassList.value = []
  }

  // 加载学校选项
  loadSchoolOptions()
}

// 班级选择弹窗相关
const classSelectVisible = ref(false)
const classSelectTitle = ref("选择班级")
const currentClassSelectType = ref("") // 当前选择类型：follow 或 manage
const schoolLoading = ref(false)
const classLoading = ref(false)

// 级联选择器相关
const cascaderValue = ref([])
const cascaderOptions = ref([])

const classColumn1s = ref([
  {
    title: "班级名称",
    dataIndex: "className",
    key: "className"
  },
  {
    title: "学习官",
    dataIndex: "learningOfficeName",
    key: "learningOfficeName"
  }
])
const classColumn2s = ref([
  {
    title: "班级名称",
    dataIndex: "className",
    key: "className"
  },
  {
    title: "组长",
    dataIndex: "learningOfficeLeaderName",
    key: "learningOfficeLeaderName",
    width: 120
  },
  {
    title: "状态",
    key: "status",
    width: 120
  }
])
const classColumns = computed(() => {
  return currentClassSelectType.value === "follow" ? classColumn1s.value : classColumn2s.value
})
const classList = ref([])
const selectedClassKeys = ref([])
const selectedClassList = ref([]) // 存储班级对象 {id, className}

/**
 * 获取学校数据
 */
const loadSchoolOptions = async () => {
  try {
    schoolLoading.value = true
    const res = await getSchoolDropdownListApi({
      PageIndex: 1,
      PageSize: 9999
    })
    if (res && res.code === 200) {
      cascaderOptions.value = res.data.items.map(school => ({
        label: school.name,
        value: school.id,
        isLeaf: false
      }))
    } else {
      cascaderOptions.value = []
    }
  } catch (error) {
    console.error("获取学校列表失败:", error)
    cascaderOptions.value = []
  } finally {
    schoolLoading.value = false
  }
}

/**
 * 级联选择器动态加载数据
 */
const loadCascaderData = async selectedOptions => {
  const targetOption = selectedOptions[selectedOptions.length - 1]

  if (selectedOptions.length === 1) {
    // 加载年级数据
    try {
      const res = await getGradeListApi(targetOption.value)
      if (res && res.code === 200) {
        targetOption.children = res.data.map(grade => ({
          label: grade.gradeDisplayName,
          value: grade.gradeStr,
          gradeLevel: grade.gradeLevel,
          gradeYear: grade.gradeYear,
          isLeaf: true // 年级是最后一级
        }))
      }
    } catch (error) {
      console.error("获取年级数据失败:", error)
    }
  }
}

/**
 * 级联选择器搜索过滤函数
 */
// const cascaderFilterOption = (inputValue, option) => {
//   if (!inputValue) return true

//   const searchValue = inputValue.toLowerCase().trim()
//   if (!searchValue) return true

//   // 检查当前选项的标签是否包含搜索关键词
//   const label = option.label ? option.label.toLowerCase() : ""
//   return label.includes(searchValue)
// }

/**
 * 级联选择器变化处理
 */
const handleCascaderChange = async (value, selectedOptions) => {
  cascaderValue.value = value
  if (value.length === 2) {
    // 选择了年级，加载班级数据到表格
    const schoolId = selectedOptions[0].value
    const gradeOption = selectedOptions[1]

    try {
      classLoading.value = true
      // const res =
      //   classSelectTitle.value === "选择服务班级"
      //     ? await getClassListApi({
      //         SchoolId: schoolId,
      //         GradeLevel: gradeOption.gradeLevel,
      //         GradeYear: gradeOption.gradeYear,
      //         PageIndex: 1,
      //         PageSize: 9999
      //       })
      //     : await getManageClassListApi({
      //         SchoolId: schoolId,
      //         GradeLevel: gradeOption.gradeLevel,
      //         GradeYear: gradeOption.gradeYear,
      //         PageIndex: 1,
      //         PageSize: 9999
      //       })
      const params = {
        SchoolId: schoolId,
        GradeLevel: gradeOption.gradeLevel,
        GradeYear: gradeOption.gradeYear,
        PageIndex: 1,
        PageSize: 9999
      }

      const res =
        classSelectTitle.value === "选择服务班级"
          ? await getClassListApi(params)
          : await getManageClassListApi(params)

      if (res && res.code === 200) {
        classList.value =
          classSelectTitle.value === "选择服务班级" ? res.data.items || [] : res.data || []
        // 重置表格选择状态，但要根据已选择的班级来设置
        selectedClassKeys.value = []
        // 如果当前表格中有已选择的班级，需要设置为选中状态
        if (selectedClassList.value.length > 0) {
          const currentSelectedKeys = classList.value
            .filter(item => selectedClassList.value.some(selected => selected.id === item.id))
            .map(item => item.id)
          selectedClassKeys.value = currentSelectedKeys
        }
      } else {
        classList.value = []
      }
    } catch (error) {
      console.error("获取班级数据失败:", error)
      classList.value = []
    } finally {
      classLoading.value = false
    }
  } else {
    // 未完整选择，清空班级表格
    classList.value = []
    selectedClassKeys.value = []
  }
}

/**
 * 表格选择变化处理
 */
const onClassSelectChange = selectedRowKeys => {
  selectedClassKeys.value = selectedRowKeys

  // 获取当前表格中选中的班级对象
  const currentSelectedClasses = classList.value
    .filter(item => selectedRowKeys.includes(item.id))
    .map(item => ({ id: item.id, className: item.className }))

  // 获取当前表格中所有班级ID
  const currentTableClassIds = classList.value.map(item => item.id)

  // 移除当前表格中未选中的班级（如果之前选中了）
  const filteredSelectedList = selectedClassList.value.filter(
    selected => !currentTableClassIds.includes(selected.id)
  )

  // 添加当前表格中新选中的班级
  selectedClassList.value = [...filteredSelectedList, ...currentSelectedClasses]
}

const removeSelectedClass = classItem => {
  selectedClassList.value = selectedClassList.value.filter(item => item.id !== classItem.id)
  // 同时取消表格中的选中状态
  selectedClassKeys.value = selectedClassKeys.value.filter(key => key !== classItem.id)
}

const handleClassSelectConfirm = () => {
  const selectClassItem = []
  // 将选中的班级设置到对应的表单字段
  selectedClassList.value.map(item => {
    selectClassItem.push(item)
    return item.className
  })
  console.log("选择的管理/服务班级数据", selectClassItem)
  // selectClassItem格式是数组： [{
  //     "id": 555,
  //     "className": "测试5班"
  // }]
  if (currentClassSelectType.value === "follow") {
    formData.value.followClassId = selectClassItem

    // 当选择服务班级后，需要从管理班级中移除重复的班级
    if (formData.value.manageClassId && formData.value.manageClassId.length > 0) {
      const selectedFollowIds = selectClassItem.map(item => item.id)
      const filteredManageClasses = formData.value.manageClassId.filter(
        manageClass => !selectedFollowIds.includes(manageClass.id)
      )

      // 如果有班级被移除，给用户提示
      if (filteredManageClasses.length !== formData.value.manageClassId.length) {
        const removedCount = formData.value.manageClassId.length - filteredManageClasses.length
        message.warning(`已从管理班级中移除 ${removedCount} 个与服务班级重复的班级`)
      }

      formData.value.manageClassId = filteredManageClasses
    }
  } else if (currentClassSelectType.value === "manage") {
    formData.value.manageClassId = selectClassItem
  }

  classSelectVisible.value = false
}

const handleClassSelectCancel = () => {
  classSelectVisible.value = false
  // 重置所有选择状态
  cascaderValue.value = []
  selectedClassKeys.value = []
  selectedClassList.value = [] // 取消时清空选择
  classList.value = []
  cascaderOptions.value = []
  currentClassSelectType.value = ""
}

const getDisplayClassNames = classData => {
  if (!classData || !Array.isArray(classData) || classData.length === 0) return ""
  return classData.map(item => item.className).join(", ")
}

// 导入相关数据
const importFormRef = ref()
const importSubmitLoading = ref(false)
const importModalVisible = ref(false)
const importFormData = ref({
  nick: undefined,
  userID: undefined
})

/**
 * 导入
 */
const importFn = record => {
  console.log(record)
  importFormData.value.userID = record.id
  importModalVisible.value = true
}

/**
 * 导入IM
 */
const handleImportSubmit = async () => {
  await importFormRef.value.validate()
  importSubmitLoading.value = true
  const reqParams = [importFormData.value]
  importLoImApi(reqParams)
    .then(res => {
      if (res.code === 200) {
        message.success("导入成功")
        importModalVisible.value = false
        getLearnofficeList()
      } else {
        message.error(res.msg || "导入失败")
      }
    })
    .finally(() => {
      importSubmitLoading.value = false
    })
}

const handleImportCancel = () => {
  importFormData.value = {
    nick: undefined,
    userID: undefined
  }
  importModalVisible.value = false
}
</script>

<style scoped lang="scss">
.learnoffice-management {
  // padding: 20px;
  background: #fff;
  border-radius: 8px;

  .title {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #262626;
  }

  .table-operations {
    // margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }

  // 表格样式优化
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }

  // 班级选择弹窗样式
  .class-select-container {
    display: flex;
    gap: 20px;
    height: 400px;

    .left-panel {
      flex: 1;
      display: flex;
      flex-direction: column;

      .school-select {
        margin-bottom: 16px;
      }

      .class-table {
        flex: 1;
        overflow: hidden;

        .tip-text {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 200px;
          color: #999;
          font-size: 14px;
          background-color: #fafafa;
          border-radius: 6px;
          border: 1px dashed #d9d9d9;
        }
      }
    }

    .right-panel {
      flex: 1;
      border-left: 1px solid #f0f0f0;
      padding-left: 20px;

      .selected-title {
        font-weight: 600;
        margin-bottom: 12px;
        color: #262626;
      }

      .selected-list {
        max-height: 350px;
        overflow-y: auto;

        .selected-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          margin-bottom: 8px;
          background-color: #f6f6f6;
          border-radius: 4px;
          font-size: 14px;

          &:hover {
            background-color: #e6f7ff;
          }
        }
      }
    }
  }
}
</style>
