# 代码质量和文件结构说明

## 📁 新的文件结构

### API 文件夹 (`src/api/`)

网络请求相关文件已移动到 `api` 文件夹：

```
src/api/
├── request.js      # Axios 请求封装
└── index.js        # API 接口统一导出
```

**使用方式：**

```javascript
// 导入特定的 API
import { userApi, roleApi } from '@/api'

// 使用 API
const users = await userApi.getUserList({ page: 1, size: 10 })
```

### 工具函数文件夹 (`src/utils/`)

工具函数库，提供常用的工具方法：

```
src/utils/
└── index.js        # 工具函数集合
```

**包含的工具函数：**

- `debounce()` - 防抖函数
- `throttle()` - 节流函数
- `deepClone()` - 深拷贝
- `formatFileSize()` - 格式化文件大小
- `generateRandomString()` - 生成随机字符串
- `formatNumber()` - 数字千分位格式化
- `validateEmail()` - 邮箱验证
- `validatePhone()` - 手机号验证
- `setStorage()` / `getStorage()` / `removeStorage()` - localStorage 操作
- `flattenTree()` - 树形数据扁平化
- `arrayToTree()` - 数组转树形结构

## 🔧 ESLint 配置

### 已配置的代码检查规则

**基础规则：**

- ✅ 检查未定义变量 (`no-undef`)
- ✅ 检查未使用变量 (`no-unused-vars`)
- ✅ 禁用 `var`，推荐使用 `const/let`
- ✅ 生产环境警告 `console` 和 `debugger`

**Vue 特定规则：**

- ✅ Vue 3 组件规范检查
- ✅ 检查未使用的组件和变量
- ✅ `v-for` 必须有 `key`
- ✅ 禁止 `v-if` 和 `v-for` 同时使用

**代码风格：**

- ✅ 单引号字符串
- ✅ 不使用分号
- ✅ 对象花括号内空格
- ✅ 关键字前后空格

### 自动修复

运行以下命令自动修复可修复的问题：

```bash
npm run lint
```

### VS Code 集成

项目已配置 `.vscode/settings.json`，支持：

- ✅ 保存时自动修复 ESLint 错误
- ✅ 保存时自动格式化代码
- ✅ Vue 文件语法高亮和错误提示

### 忽略文件

以下文件会被 ESLint 忽略：

- `auto-imports.d.ts` - 自动导入类型声明
- `components.d.ts` - 组件类型声明
- `dist/` - 构建输出目录
- `node_modules/` - 依赖包目录

## 🚨 错误提示说明

### 编辑器中的错误提示

配置完成后，VS Code 会在编辑器中显示：

- 🔴 **红色波浪线** - 语法错误和严重问题
- 🟡 **黄色波浪线** - 警告和代码风格问题
- 💡 **灯泡图标** - 可自动修复的问题

### 常见错误类型

1. **未定义变量** (`no-undef`)

   ```javascript
   // ❌ 错误：nihao 未定义
   console.log(nihao)

   // ✅ 正确：先定义变量
   const nihao = 'Hello'
   console.log(nihao)
   ```

2. **未使用变量** (`no-unused-vars`)

   ```javascript
   // ❌ 警告：变量定义但未使用
   const unusedVar = 'test'

   // ✅ 正确：删除未使用的变量或使用它
   const usedVar = 'test'
   console.log(usedVar)
   ```

3. **Vue 组件未使用** (`vue/no-unused-components`)
   ```vue
   <script setup>
   // ❌ 警告：导入但未在模板中使用
   import UnusedComponent from './UnusedComponent.vue'
   </script>
   ```

## 📝 最佳实践

### 1. 代码提交前检查

```bash
# 运行 ESLint 检查
npm run lint

# 如果有错误，修复后再提交
git add .
git commit -m "fix: 修复代码质量问题"
```

### 2. 使用工具函数

```javascript
// ✅ 推荐：使用项目提供的工具函数
import { debounce, validateEmail } from '@/utils'

// 或者通过自动导入直接使用
const debouncedFn = debounce(() => {
  // 处理逻辑
}, 300)
```

### 3. API 调用规范

```javascript
// ✅ 推荐：使用统一的 API 接口
import { userApi } from '@/api'

const handleGetUsers = async () => {
  try {
    const response = await userApi.getUserList()
    // 处理响应
  } catch (error) {
    // 处理错误
  }
}
```

### 4. 错误处理

```javascript
// ✅ 推荐：始终处理可能的错误
try {
  const result = await someAsyncOperation()
  return result
} catch (error) {
  console.error('操作失败:', error)
  message.error('操作失败，请重试')
}
```

## 🔄 持续改进

随着项目发展，可以考虑：

1. 添加 TypeScript 支持
2. 集成更多代码质量工具（如 SonarQube）
3. 添加单元测试和测试覆盖率检查
4. 配置 Git hooks 进行提交前检查
