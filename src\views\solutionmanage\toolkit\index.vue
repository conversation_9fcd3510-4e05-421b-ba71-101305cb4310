<!-- 工具包管理 -->
<template>
  <div class="toolkit-management">
    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="适用对象" class="marginBot">
          <a-select
            v-model:value="searchForm.ToolObjectType"
            placeholder="请选择对象"
            style="width: 150px"
            allow-clear
          >
            <a-select-option v-for="item in toolObjectType" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="适用年级" class="marginBot">
          <a-select
            v-model:value="searchForm.SolutionSemesterEnums"
            placeholder="请选择年级"
            style="width: 330px"
            allow-clear
            show-search
            :max-tag-count="2"
            mode="multiple"
            :filter-option="filterOption"
            :options="grade"
          >
          </a-select>
        </a-form-item>

        <a-form-item label="适用班级类型" class="marginBot">
          <a-select
            v-model:value="searchForm.ToolClassType"
            placeholder="请选择适用班级类型"
            style="width: 200px"
            allow-clear
          >
            <a-select-option v-for="item in classType" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="工具名称" class="marginBot">
          <a-input
            v-model:value="searchForm.ToolName"
            placeholder="请输入工具名称"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>

        <a-form-item class="marginBot">
          <a-button type="primary" html-type="submit" :loading="loading"> 搜索 </a-button>
          <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮区域 -->
    <a-card class="operation-card" :bordered="false">
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd"> 添加工具 </a-button>
        <a-button @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0" danger>
          删除
        </a-button>
        <span class="selected-info" v-if="selectedRowKeys.length > 0">
          已选择 {{ selectedRowKeys.length }} 项
        </span>
      </div>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :row-selection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'toolSize'">
            {{ formatFileSize(record.toolSize) }}
          </template>

          <template v-if="column.key === 'problemObj'">
            {{ getObjectTypeName(record.problemObj) }}
          </template>

          <template v-if="column.key === 'toolKitSemesters'">
            <div class="grade-tags-container">
              <!-- 始终显示的前3个标签 -->
              <a-tag
                v-for="(item, index) in record.toolKitSemesters.slice(0, 3)"
                :key="item.id"
                color="green"
                style="margin-bottom: 4px; margin-right: 4px"
              >
                {{ item.solutionSemesterName }}
              </a-tag>

              <!-- 当超过3个时的处理 -->
              <template v-if="record.toolKitSemesters.length > 3">
                <!-- 展开状态：显示剩余的所有标签 -->
                <template v-if="expandedRows[record.id]">
                  <a-tag
                    v-for="(item, index) in record.toolKitSemesters.slice(3)"
                    :key="item.id"
                    color="green"
                    style="margin-bottom: 4px; margin-right: 4px"
                  >
                    {{ item.solutionSemesterName }}
                  </a-tag>
                  <a-button
                    type="link"
                    size="small"
                    @click="toggleExpand(record.id)"
                    style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                  >
                    收起
                  </a-button>
                </template>

                <!-- 收起状态：显示"展开"按钮和剩余数量 -->
                <template v-else>
                  <a-button
                    type="link"
                    size="small"
                    @click="toggleExpand(record.id)"
                    style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                  >
                    +{{ record.toolKitSemesters.length - 3 }}个
                  </a-button>
                </template>
              </template>
            </div>
          </template>

          <template v-if="column.key === 'toolClassType'">
            {{ getClassTypeName(record.toolClassType) }}
          </template>

          <template v-if="column.key === 'upTime'">
            {{ formatDate(record.upTime) }}
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleDetail(record)"> 详情 </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="工具包详情"
      width="900px"
      :footer="null"
      @cancel="handleDetailCancel"
    >
      <a-spin :spinning="detailLoading" tip="加载详情数据...">
        <div class="detail-content">
          <!-- 基本信息 -->
          <a-descriptions title="基本信息" :column="2" bordered>
            <a-descriptions-item label="工具名称">
              {{ detailData.toolName || "-" }}
            </a-descriptions-item>
            <a-descriptions-item label="适用对象">
              {{ getObjectTypeName(detailData.problemObj) }}
            </a-descriptions-item>
            <a-descriptions-item label="适用年级">
              <div v-if="detailData.toolKitSemesters && detailData.toolKitSemesters.length > 0">
                <div class="grade-tags-container">
                  <!-- 始终显示的前3个标签 -->
                  <a-tag
                    v-for="(item, index) in detailData.toolKitSemesters.slice(0, 3)"
                    :key="item.id"
                    color="green"
                    style="margin-bottom: 4px; margin-right: 4px"
                  >
                    {{ item.solutionSemesterName }}
                  </a-tag>

                  <!-- 当超过3个时的处理 -->
                  <template v-if="detailData.toolKitSemesters.length > 3">
                    <!-- 展开状态：显示剩余的所有标签 -->
                    <template v-if="detailExpanded">
                      <a-tag
                        v-for="(item, index) in detailData.toolKitSemesters.slice(3)"
                        :key="item.id"
                        color="green"
                        style="margin-bottom: 4px; margin-right: 4px"
                      >
                        {{ item.solutionSemesterName }}
                      </a-tag>
                      <a-button
                        type="link"
                        size="small"
                        @click="detailExpanded = false"
                        style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                      >
                        收起
                      </a-button>
                    </template>

                    <!-- 收起状态：显示"展开"按钮和剩余数量 -->
                    <template v-else>
                      <a-button
                        type="link"
                        size="small"
                        @click="detailExpanded = true"
                        style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                      >
                        +{{ detailData.toolKitSemesters.length - 3 }}个
                      </a-button>
                    </template>
                  </template>
                </div>
              </div>
              <span v-else>-</span>
            </a-descriptions-item>
            <a-descriptions-item label="适用班级类型">
              {{ getClassTypeName(detailData.toolClassType) }}
            </a-descriptions-item>
          </a-descriptions>

          <!-- 关联解决方案 -->
          <div class="solution-section" style="margin-top: 24px">
            <h3 style="margin-bottom: 16px">关联解决方案</h3>
            <a-table
              :columns="solutionColumns"
              :data-source="solutionTableData"
              :pagination="false"
              size="small"
              :scroll="{ x: 'max-content' }"
            >
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'index'">
                  {{ index + 1 }}
                </template>
                <template v-if="column.key === 'upTime'">
                  {{ formatDate(record.upTime) }}
                </template>
                <template v-if="column.key === 'problemObj'">
                  {{ getObjectTypeName(record.problemObj) }}
                </template>
                <template v-if="column.key === 'problemSemesters'">
                  <div class="grade-tags-container">
                    <!-- 始终显示的前3个标签 -->
                    <a-tag
                      v-for="(item, index) in record.problemSemesters.slice(0, 3)"
                      :key="item.id"
                      color="green"
                      style="margin-bottom: 4px; margin-right: 4px"
                    >
                      {{ item.solutionSemesterName }}
                    </a-tag>

                    <!-- 当超过3个时的处理 -->
                    <template v-if="record.problemSemesters.length > 3">
                      <!-- 展开状态：显示剩余的所有标签 -->
                      <template v-if="solutionExpandedRows[record.id]">
                        <a-tag
                          v-for="(item, index) in record.problemSemesters.slice(3)"
                          :key="item.id"
                          color="green"
                          style="margin-bottom: 4px; margin-right: 4px"
                        >
                          {{ item.solutionSemesterName }}
                        </a-tag>
                        <a-button
                          type="link"
                          size="small"
                          @click="toggleSolutionExpand(record.id)"
                          style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                        >
                          收起
                        </a-button>
                      </template>

                      <!-- 收起状态：显示"展开"按钮和剩余数量 -->
                      <template v-else>
                        <a-button
                          type="link"
                          size="small"
                          @click="toggleSolutionExpand(record.id)"
                          style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                        >
                          +{{ record.problemSemesters.length - 3 }}个
                        </a-button>
                      </template>
                    </template>
                  </div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </a-spin>
    </a-modal>

    <!-- 添加工具弹窗 -->
    <a-modal
      v-model:open="addModalVisible"
      title="添加工具"
      width="600px"
      :confirm-loading="addSubmitLoading"
      @ok="handleAddSubmit"
      @cancel="handleAddCancel"
    >
      <a-form ref="addFormRef" :model="addFormData" :rules="addFormRules" layout="vertical">
        <a-form-item label="工具适用对象" name="problemObj">
          <a-select
            v-model:value="addFormData.problemObj"
            placeholder="请选择工具适用对象"
            style="width: 100%"
          >
            <a-select-option v-for="item in toolObjectType" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="工具适用年级" name="solutionSemesters">
          <div class="grade-select-wrapper" style="display: flex; align-items: center; gap: 10px">
            <a-select
              v-model:value="addFormData.solutionSemesters"
              mode="multiple"
              placeholder="请选择工具适用年级"
              style="width: 100%"
              :max-tag-count="3"
              allow-clear
              show-search
              :options="grade"
              :filter-option="filterOption"
              @change="handleGradeSelectChange"
            >
              <!-- <a-select-option v-for="item in grade" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option> -->
            </a-select>
            <div class="grade-select-header" style="width: 200px">
              <a-checkbox
                v-model:checked="selectAllGrades"
                :indeterminate="indeterminate"
                @change="handleSelectAllGrades"
              >
                全选
              </a-checkbox>
              <span class="selected-count" style="color: #8d8d8d; font-size: 12px">
                已选择 {{ addFormData.solutionSemesters.length }} 项
              </span>
            </div>
          </div>
        </a-form-item>

        <a-form-item label="工具适用班级类型" name="toolClassType">
          <a-radio-group v-model:value="addFormData.toolClassType">
            <a-radio v-for="item in classType" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="上传工具" name="uploadFiles">
          <div class="upload-section">
            <!-- 文件上传区域 -->
            <a-upload
              :file-list="uploadFileList"
              :before-upload="beforeUpload"
              :show-upload-list="false"
              accept=".png,.jpg,.jpeg,.gif,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar"
              multiple
            >
              <a-button type="dashed" style="width: 100%; height: 80px" :loading="uploadLoading">
                <div style="display: flex; flex-direction: column; align-items: center; gap: 8px">
                  <UploadOutlined style="font-size: 20px" />
                  <span style="font-size: 12px">点击上传工具文件</span>
                  <span style="font-size: 10px; color: #999">
                    支持 PNG、JPG、PDF、DOC、XLS、ZIP 等格式，文件大小不超过 100MB
                  </span>
                </div>
              </a-button>
            </a-upload>

            <!-- 文件列表表格 -->
            <div class="file-list-table" style="margin-top: 16px">
              <a-table
                :columns="fileTableColumns"
                :data-source="uploadFileList"
                :pagination="false"
                size="small"
                row-key="uid"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'name'">
                    <span :title="record.name">{{ record.name }}</span>
                  </template>
                  <template v-if="column.key === 'status'">
                    <a-tag v-if="record.status === 'uploading'" color="processing">上传中</a-tag>
                    <a-tag v-else-if="record.status === 'done'" color="success">上传成功</a-tag>
                    <a-tag v-else-if="record.status === 'error'" color="error">上传失败</a-tag>
                    <a-tag v-else color="default">待上传</a-tag>
                  </template>
                  <template v-if="column.key === 'action'">
                    <a-button type="link" size="small" danger @click="handleDeleteFile(record)">
                      删除
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑工具弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑工具"
      width="600px"
      :confirm-loading="editSubmitLoading"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-spin :spinning="editLoading" tip="加载工具数据...">
        <a-form ref="editFormRef" :model="editFormData" :rules="editFormRules" layout="vertical">
          <a-form-item label="工具适用对象" name="problemObj">
            <a-select
              v-model:value="editFormData.problemObj"
              placeholder="请选择工具适用对象"
              style="width: 100%"
            >
              <a-select-option v-for="item in toolObjectType" :key="item.value" :value="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="工具适用年级" name="solutionSemesters">
            <div class="grade-select-wrapper" style="display: flex; align-items: center; gap: 10px">
              <a-select
                v-model:value="editFormData.solutionSemesters"
                mode="multiple"
                placeholder="请选择工具适用年级"
                style="width: 100%"
                :max-tag-count="3"
                allow-clear
                show-search
                :options="grade"
                :filter-option="filterOption"
                @change="handleEditGradeSelectChange"
              >
              </a-select>
              <div class="grade-select-header" style="width: 200px">
                <a-checkbox
                  v-model:checked="editSelectAllGrades"
                  :indeterminate="editIndeterminate"
                  @change="handleEditSelectAllGrades"
                >
                  全选
                </a-checkbox>
                <span class="selected-count" style="color: #8d8d8d; font-size: 12px">
                  已选择 {{ editFormData.solutionSemesters.length }} 项
                </span>
              </div>
            </div>
          </a-form-item>

          <a-form-item label="工具适用班级类型" name="toolClassType">
            <a-radio-group v-model:value="editFormData.toolClassType">
              <a-radio v-for="item in classType" :key="item.value" :value="item.value">
                {{ item.text }}
              </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="上传工具" name="uploadFiles">
            <div class="upload-section">
              <!-- 文件上传区域 -->
              <a-upload
                :file-list="uploadFileList"
                :before-upload="beforeUpload"
                :show-upload-list="false"
                accept=".png,.jpg,.jpeg,.gif,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar"
                multiple
              >
                <a-button
                  type="dashed"
                  style="width: 100%; height: 80px"
                  :loading="uploadLoading"
                  disabled
                >
                  <div style="display: flex; flex-direction: column; align-items: center; gap: 8px">
                    <UploadOutlined style="font-size: 20px" />
                    <span style="font-size: 12px">点击上传工具文件</span>
                    <span style="font-size: 10px; color: #999">
                      支持 PNG、JPG、PDF、DOC、XLS、ZIP 等格式，文件大小不超过 100MB
                    </span>
                  </div>
                </a-button>
              </a-upload>

              <!-- 文件列表表格 -->
              <div class="file-list-table" style="margin-top: 16px">
                <a-table
                  :columns="fileTableColumns"
                  :data-source="uploadFileList"
                  :pagination="false"
                  size="small"
                  row-key="uid"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'name'">
                      <span :title="record.name">{{ record.name }}</span>
                    </template>
                    <template v-if="column.key === 'status'">
                      <a-tag v-if="record.status === 'uploading'" color="processing">上传中</a-tag>
                      <a-tag v-else-if="record.status === 'done'" color="success">上传成功</a-tag>
                      <a-tag v-else-if="record.status === 'error'" color="error">上传失败</a-tag>
                      <a-tag v-else color="default">待上传</a-tag>
                    </template>
                    <template v-if="column.key === 'action'">
                      <a-button
                        disabled
                        type="link"
                        size="small"
                        danger
                        @click="handleDeleteFile(record)"
                      >
                        删除
                      </a-button>
                    </template>
                  </template>
                </a-table>
              </div>
            </div>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>

<script setup name="Toolkit">
import { ref, reactive, onMounted, nextTick } from "vue"
import { message } from "ant-design-vue"
import {
  getToolkitListApi,
  getSolutionGradeListApi,
  getToolObjectTypeListApi,
  getSysFileConfigApi,
  uploadFileApi,
  getToolkitDetailApi,
  deleteToolkitApi,
  addToolkitApi,
  getToolClassTypeListApi,
  deleteFileApi,
  editToolkitApi
} from "@/api"
import dayjs from "dayjs"
import { ExclamationCircleOutlined } from "@ant-design/icons-vue"
import { createVNode } from "vue"
import { Modal } from "ant-design-vue"
import { UploadOutlined } from "@ant-design/icons-vue"

// getSysFileConfigApi("png")
// 响应式数据
const loading = ref(false)
const dataSource = ref([])
const selectedRowKeys = ref([])

// 搜索表单
const searchForm = reactive({
  ToolObjectType: undefined,
  SolutionSemesterEnums: undefined,
  ToolClassType: undefined,
  ToolName: ""
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条数据，显示第 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ["10", "20", "50", "100"]
})

// 表格列配置
const columns = [
  {
    title: "工具名称",
    dataIndex: "toolName",
    key: "toolName",
    width: 150
  },
  {
    title: "修改时间",
    dataIndex: "upTime",
    key: "upTime",
    width: 150
  },
  {
    title: "大小",
    dataIndex: "toolSize",
    key: "toolSize",
    width: 100
  },
  {
    title: "适用对象",
    dataIndex: "problemObj",
    key: "problemObj",
    width: 120
  },
  {
    title: "适用年级",
    dataIndex: "toolKitSemesters",
    key: "toolKitSemesters",
    width: 120
  },
  {
    title: "适用班级类型",
    dataIndex: "toolClassType",
    key: "toolClassType",
    width: 130
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right"
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: keys => {
    selectedRowKeys.value = keys
  }
}

/**
 * 适用班级类型
 */
const classType = ref([
  {
    text: "普通本科",
    value: 1
  },
  {
    text: "重点本科",
    value: 2
  }
])

/**
 * 年级
 */
const grade = ref([])

/**
 * 对象
 */
const toolObjectType = ref([])

/**
 * 详情弹窗相关
 */
const detailModalVisible = ref(false)
const detailLoading = ref(false)
const detailData = reactive({
  toolName: "",
  problemObj: undefined,
  toolKitSemesters: [],
  toolClassType: undefined
})
const solutionTableData = ref([])

/**
 * 解决方案列配置
 */
const solutionColumns = [
  {
    title: "序号",
    key: "index",
    width: 60
  },
  {
    title: "问题描述",
    dataIndex: "problemTitle",
    key: "problemTitle",
    width: 200
  },
  {
    title: "修改时间",
    dataIndex: "upTime",
    key: "upTime",
    width: 150
  },
  {
    title: "适用对象",
    dataIndex: "problemObj",
    key: "problemObj",
    width: 120
  },
  {
    title: "适用年级",
    dataIndex: "problemSemesters",
    key: "problemSemesters",
    width: 120
  }
]

/**
 * 添加工具弹窗相关
 */
const addModalVisible = ref(false)
const addSubmitLoading = ref(false)
const addFormRef = ref(null)
const addFormData = reactive({
  problemObj: undefined,
  solutionSemesters: [],
  toolClassType: undefined,
  uploadFiles: [] // 新增上传文件列表
})
const addFormRules = {
  problemObj: [{ required: true, message: "请选择工具适用对象" }],
  solutionSemesters: [{ type: "array", required: true, message: "请选择工具适用年级" }],
  toolClassType: [{ required: true, message: "请选择工具适用班级类型" }],
  uploadFiles: [{ type: "array", required: true, message: "请上传工具文件" }] // 新增上传文件规则
}

/**
 * 全选年级
 */
const selectAllGrades = ref(false)
const indeterminate = ref(false)

/**
 * 处理年级全选
 */
const handleSelectAllGrades = checked => {
  const checkedStatus = checked.target.checked
  if (checkedStatus) {
    // 创建新数组确保响应式更新
    addFormData.solutionSemesters = [...grade.value.map(item => item.value)]
  } else {
    // 创建新数组确保响应式更新
    addFormData.solutionSemesters = []
  }
  indeterminate.value = false
}

/**
 * 处理年级选择变化
 */
const handleGradeSelectChange = selectedValues => {
  addFormData.solutionSemesters = selectedValues
  selectAllGrades.value = selectedValues.length === grade.value.length && selectedValues.length > 0
  indeterminate.value = selectedValues.length > 0 && selectedValues.length < grade.value.length
}

/**
 * 编辑工具弹窗相关
 */
const editModalVisible = ref(false)
const editLoading = ref(false)
const editSubmitLoading = ref(false)
const editFormRef = ref(null)
const editFormData = reactive({
  id: undefined,
  problemObj: undefined,
  solutionSemesters: [],
  toolClassType: undefined,
  uploadFiles: [] // 编辑时上传文件列表
})
const editFormRules = {
  problemObj: [{ required: true, message: "请选择工具适用对象" }],
  solutionSemesters: [{ type: "array", required: true, message: "请选择工具适用年级" }],
  toolClassType: [{ required: true, message: "请选择工具适用班级类型" }],
  uploadFiles: [{ type: "array", required: true, message: "请上传工具文件" }]
}
const editSelectAllGrades = ref(false)
const editIndeterminate = ref(false)

/**
 * 处理编辑年级全选
 */
const handleEditSelectAllGrades = checked => {
  const checkedStatus = checked.target.checked
  if (checkedStatus) {
    // 创建新数组确保响应式更新
    editFormData.solutionSemesters = [...grade.value.map(item => item.value)]
  } else {
    // 创建新数组确保响应式更新
    editFormData.solutionSemesters = []
  }
  editIndeterminate.value = false
}

/**
 * 处理编辑年级选择变化
 */
const handleEditGradeSelectChange = selectedValues => {
  editFormData.solutionSemesters = selectedValues
  editSelectAllGrades.value =
    selectedValues.length === grade.value.length && selectedValues.length > 0
  editIndeterminate.value = selectedValues.length > 0 && selectedValues.length < grade.value.length
}

/**
 * 获取表格列表数据
 */
const getToolkitList = async () => {
  try {
    loading.value = true

    const params = {
      PageSize: pagination.pageSize,
      PageIndex: pagination.current,
      ...searchForm
    }
    // 过滤掉空值
    Object.keys(params).forEach(key => {
      if (params[key] === "" || params[key] === undefined || params[key] === null) {
        delete params[key]
      }
    })
    if (params.SolutionSemesterEnums === 0) {
      delete params.SolutionSemesterEnums
    }
    const res = await getToolkitListApi(params)

    if (res && res.code === 200) {
      dataSource.value = res.data?.items || []
      pagination.total = res.data?.total || 0
    } else {
      message.error(res?.msg || "获取数据失败")
    }
  } catch (error) {
    console.error("获取工具包列表失败:", error)
    // message.error("获取数据失败")
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  getToolkitList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (typeof searchForm[key] === "string") {
      searchForm[key] = ""
    } else {
      searchForm[key] = undefined
    }
  })
  pagination.current = 1
  getToolkitList()
}

/**
 * 表格变化处理
 */
const handleTableChange = pag => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  getToolkitList()
}

/**
 * 添加工具
 */
const handleAdd = () => {
  console.log("添加工具")
  addModalVisible.value = true
}

/**
 * 批量删除
 */
const handleBatchDelete = () => {
  console.log("删除", selectedRowKeys.value)
  Modal.confirm({
    title: "确定删除这些工具吗？",
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      console.log("OK")
      deleteToolkitApi(selectedRowKeys.value).then(res => {
        if (res.code === 200) {
          message.success("删除成功")
          getToolkitList()
          selectedRowKeys.value = []
        } else {
          message.error(res?.msg || "删除失败")
        }
      })
    },
    onCancel() {
      console.log("Cancel")
    },
    class: "test"
  })
}

/**
 * 查看详情
 */
const handleDetail = record => {
  detailLoading.value = true
  getToolkitDetailApi(record.id)
    .then(res => {
      if (res.code === 200) {
        detailData.toolName = res.data.toolName
        detailData.problemObj = res.data.problemObj
        detailData.toolKitSemesters = res.data.toolKitSemesters || []
        detailData.toolClassType = res.data.toolClassType

        // 获取关联解决方案
        solutionTableData.value = res.data.solutionLists || []
        detailModalVisible.value = true
      } else {
        message.error(res?.msg || "获取详情失败")
      }
    })
    .finally(() => {
      detailLoading.value = false
    })
}

/**
 * 编辑
 */
const handleEdit = record => {
  console.log("编辑", record)
  editModalVisible.value = true
  editLoading.value = true

  // 获取工具详情数据
  getToolkitDetailApi(record.id)
    .then(res => {
      if (res.code === 200) {
        const data = res.data

        // 填充表单数据
        editFormData.id = data.id
        editFormData.problemObj = data.problemObj
        editFormData.toolClassType = data.toolClassType

        // 处理年级数据
        if (data.toolKitSemesters && data.toolKitSemesters.length > 0) {
          editFormData.solutionSemesters = data.toolKitSemesters.map(
            item => item.solutionSemesterEnum
          )
        } else {
          editFormData.solutionSemesters = []
        }

        // 更新年级全选状态
        editSelectAllGrades.value =
          editFormData.solutionSemesters.length === grade.value.length &&
          editFormData.solutionSemesters.length > 0
        editIndeterminate.value =
          editFormData.solutionSemesters.length > 0 &&
          editFormData.solutionSemesters.length < grade.value.length

        // 处理上传文件
        if (data.fileinfo) {
          uploadFileList.value = [
            {
              uid: Date.now() + Math.random(),
              name: data.fileinfo.fileName,
              status: "done",
              sysFileId: data.fileinfo.id,
              size: data.toolSize
            }
          ]
          editFormData.uploadFiles = uploadFileList.value
        }
        console.log("编辑数据填充完成", editFormData)
      } else {
        message.error(res?.msg || "获取工具详情失败")
      }
    })
    .finally(() => {
      editLoading.value = false
    })
}

/**
 * 编辑工具提交
 */
const handleEditSubmit = () => {
  editFormRef.value.validate().then(async () => {
    editSubmitLoading.value = true
    try {
      console.log("编辑工具表单数据", editFormData)
      let reqParams = {}
      const fileInfo = editFormData.uploadFiles[0]
      reqParams = {
        id: editFormData.id,
        // 适用年级
        solutionSemesters: editFormData.solutionSemesters,
        // 适用对象
        problemObj: editFormData.problemObj,
        // 适用班级类型
        toolClassType: editFormData.toolClassType,

        toolName:
          fileInfo.name.lastIndexOf(".") !== -1
            ? fileInfo.name.substring(0, fileInfo.name.lastIndexOf("."))
            : fileInfo.name,
        toolType: fileInfo.name.substring(fileInfo.name.lastIndexOf(".") + 1).toLowerCase(),
        // 文件上传后id
        sysFileId: fileInfo.sysFileId,
        // 文件大小： 字节转kb
        toolSize: fileInfo.size
      }
      console.log("编辑工具提交数据", reqParams)
      // 这里调用编辑接口
      const res = await editToolkitApi(reqParams)
      if (res.code === 200) {
        message.success("编辑成功")
        editModalVisible.value = false
        clearEditFormData()
        getToolkitList()
      } else {
        message.error(res?.msg || "编辑失败")
      }
    } catch (error) {
      console.error("编辑工具失败:", error)
    } finally {
      editSubmitLoading.value = false
    }
  })
}

/**
 * 编辑工具取消
 */
const handleEditCancel = () => {
  editModalVisible.value = false
  clearEditFormData()
}

/**
 * 清空编辑工具表单数据
 */
const clearEditFormData = () => {
  // 重置表单
  editFormRef.value?.resetFields()

  // 清空表单数据
  editFormData.problemObj = undefined
  editFormData.solutionSemesters = []
  editFormData.toolClassType = undefined

  // 清空年级选择状态
  editSelectAllGrades.value = false
  editIndeterminate.value = false
  // 清空文件
  uploadFileList.value = []
}

/**
 * 详情弹窗取消
 */
const handleDetailCancel = () => {
  detailModalVisible.value = false
  detailData.toolName = ""
  detailData.problemObj = undefined
  detailData.toolKitSemesters = []
  detailData.toolClassType = undefined
  solutionTableData.value = []
  // 重置详情弹窗中年级标签的展开状态
  detailExpanded.value = false
  // 重置关联解决方案表格中年级标签的展开状态
  solutionExpandedRows.value = {}
}

/**
 * 添加工具提交
 */
const handleAddSubmit = () => {
  addFormRef.value.validate().then(async () => {
    addSubmitLoading.value = true
    try {
      console.log("添加工具提交", addFormData)
      const reqarams = []
      addFormData.uploadFiles.forEach(item => {
        if (item.status === "done") {
          reqarams.push({
            // 适用年级
            solutionSemesters: addFormData.solutionSemesters,
            // 适用对象
            problemObj: addFormData.problemObj,
            // 适用班级类型
            toolClassType: addFormData.toolClassType,

            toolName:
              item.name.lastIndexOf(".") !== -1
                ? item.name.substring(0, item.name.lastIndexOf("."))
                : item.name,
            toolType: item.name.substring(item.name.lastIndexOf(".") + 1).toLowerCase(),
            // 文件上传后id
            sysFileId: item.sysFileId,
            // 文件大小： 字节转kb
            toolSize: item.size
          })
        }
      })
      console.log("添加工具提交数据", reqarams)

      // 测试添加工具参数
      // addToolkitApi([
      //   {
      //     // 适用年级
      //     solutionSemesters: [71],
      //     // 适用对象
      //     problemObj: 2,
      //     // 适用班级类型
      //     toolClassType: 3

      //     toolName: "logo3",
      //     toolType: "png",
      //     // 文件上传后id
      //     sysFileId: 695420051808325,
      //     // 文件大小： 字节转kb
      //     toolSize: Math.trunc(253225 / 1024),

      //   }
      // ])

      const res = await addToolkitApi(reqarams)
      if (res.code === 200) {
        message.success("添加成功")
        addModalVisible.value = false
        clearAddFormData()
        getToolkitList()
      } else {
        message.error(res?.msg || "添加失败")
      }
    } catch (error) {
      console.error("添加工具失败:", error)
      // message.error("添加失败")
    } finally {
      addSubmitLoading.value = false
    }
  })
}

/**
 * 清空添加工具表单数据
 */
const clearAddFormData = () => {
  // 重置表单
  addFormRef.value?.resetFields()

  // 清空表单数据
  addFormData.problemObj = undefined
  addFormData.solutionSemesters = []
  addFormData.toolClassType = undefined
  addFormData.uploadFiles = []

  // 清空年级选择状态
  selectAllGrades.value = false
  indeterminate.value = false

  // 清空上传文件列表
  uploadFileList.value = []
  uploadLoading.value = false
}

/**
 * 添加工具取消
 */
const handleAddCancel = () => {
  addModalVisible.value = false
  clearAddFormData()
}

/**
 * 过滤年级选项
 */
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

/**
 * 格式化日期
 */
const formatDate = date => {
  if (!date) return "-"
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss")
}

/**
 * 获取对象类型名称
 */
const getObjectTypeName = value => {
  const item = toolObjectType.value.find(item => item.value === value)
  return item ? item.text : "-"
}

/**
 * 获取班级类型名称
 */
const getClassTypeName = value => {
  const item = classType.value.find(item => item.value === value)
  return item ? item.text : "-"
}

/**
 * 格式化文件大小
 */
const formatFileSize = size => {
  if (size === null || size === undefined || size === 0) return "-"

  // 输入的size是字节单位，需要转换为合适的单位显示
  if (size < 1024) {
    return size + " B"
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB"
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + " MB"
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + " GB"
  }
}

/**
 * 上传文件到 OSS
 * @param {File} file - 需要上传的文件
 * @param {string} presignedUrl - 预签名 URL
 */
const upload = async (file, presignedUrl) => {
  console.log("开始上传文件:", file.name, "大小:", file.size)

  try {
    // 使用ArrayBuffer方式上传（已验证有效的方法）
    return await uploadWithArrayBuffer(file, presignedUrl)
  } catch (error) {
    console.error("ArrayBuffer方式失败，尝试简单PUT方式:", error.message)

    // 备用方案：最简单的PUT请求
    return await uploadMethod1(file, presignedUrl)
  }
}

/**
 * 使用ArrayBuffer上传（主要方法）
 */
const uploadWithArrayBuffer = async (file, presignedUrl) => {
  console.log("使用ArrayBuffer方式上传...")

  const arrayBuffer = await file.arrayBuffer()

  const response = await fetch(presignedUrl, {
    method: "PUT",
    body: arrayBuffer,
    headers: {
      "Content-Length": file.size.toString()
    }
  })
  console.log("ArrayBuffer上传响应:", response)

  if (!response.ok) {
    const errorText = await response.text().catch(() => "unknown")
    throw new Error(`Upload failed, status: ${response.status}, details: ${errorText}`)
  }

  console.log("ArrayBuffer上传成功!")
  return response
}

// 方法1: 最简单的PUT请求
const uploadMethod1 = async (file, presignedUrl) => {
  const response = await fetch(presignedUrl, {
    method: "PUT",
    body: file
    // 故意不设置任何headers
  })

  console.log("方法1响应状态:", response.status)
  if (!response.ok) {
    const errorText = await response.text().catch(() => "unknown")
    throw new Error(`Status: ${response.status}, Details: ${errorText}`)
  }
  return response
}

/**
 * 获取文件对应的OSS配置
 * @param {File} file - 文件对象
 */
const getOssConfigForFile = async file => {
  try {
    // 获取文件扩展名
    const fileName = file.name
    const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()
    // fileExtension: png、jpg等
    console.log("获取OSS配置，文件扩展名:", fileExtension)
    //根据上传文件扩展名获取OSS配置
    const res = await getSysFileConfigApi(fileExtension)

    if (res.code === 200) {
      // res.data格式:
      //       {
      //     "filePath": "https://quanxue-oa.oss-cn-chengdu.aliyuncs.com/20250708/1751938472526.png",
      //     "fileSize": 102400,
      //     "uploadUrl": "https://quanxue-oa.oss-cn-chengdu.aliyuncs.com/20250708/1751938472526.png?x-oss-signature-version=OSS4-HMAC-SHA256&x-oss-date=20250708T013432Z&x-oss-expires=32399&x-oss-credential=LTAI5tCVv48c3cjn8W2gvcU5%2F20250708%2Fcn-chengdu%2Foss%2Faliyun_v4_request&x-oss-signature=ebb7479694f93a77125483d0c42fbebb9dbc95cd866192b971ae5b6bd704e60a"
      // }
      console.log("OSS配置获取成功:", res.data)
      return res.data
    } else {
      throw new Error(`获取OSS配置失败: ${res.msg || "未知错误"}`)
    }
  } catch (error) {
    console.error("获取OSS配置失败:", error)
    throw error
  }
}
// 文件上传，返回文件id
const getFileId = async (file, url) => {
  const fileName = file.name
  const type = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()

  console.log("获取文件id请求参数", {
    file_name: fileName,
    file_path: url,
    file_type: type
  })
  //   file_name: "234234.png",
  //   file_path: "https://quanxue-oa.oss-cn-chengdu.aliyuncs.com/20250707/2.png",
  //   file_type: "png"
  const res = await uploadFileApi({
    file_name: fileName,
    file_path: url,
    file_type: type
  })
  if (res.code === 200) {
    console.log("文件上传成功id:", res.data)
    return res.data // 返回文件ID
  } else {
    message.error(res?.msg || "文件上传失败")
    throw new Error(res?.msg || "文件上传失败")
  }
}
//删除已上传文件
const deleteFile = async fileId => {
  const res = await deleteFileApi(fileId)
  if (res.code === 200) {
    console.log("文件删除成功")
    return true
  } else {
    throw new Error(res?.msg || "删除文件失败")
  }
}

const uploadLoading = ref(false)
const uploadFileList = ref([])

const beforeUpload = async file => {
  // 检查文件大小限制（100M = 100 * 1024 * 1024 bytes）
  const maxSize = 100 * 1024 * 1024 // 100MB
  if (file.size > maxSize) {
    message.error(
      `文件大小不能超过 100MB，当前文件大小为 ${(file.size / 1024 / 1024).toFixed(2)}MB`
    )
    return false
  }

  uploadLoading.value = true

  // 添加到文件列表，状态为上传中
  const fileItem = {
    uid: file.uid || Date.now() + Math.random(),
    name: file.name,
    status: "uploading",
    size: file.size,
    originFile: file
  }
  uploadFileList.value.push(fileItem)

  try {
    const ossConfig = await getOssConfigForFile(file)
    if (!ossConfig || !ossConfig.uploadUrl) {
      throw new Error("无法获取有效的OSS配置")
    }

    await upload(file, ossConfig.uploadUrl)
    const sysFileId = await getFileId(file, ossConfig.filePath)

    // 更新文件状态为成功
    const index = uploadFileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index > -1) {
      uploadFileList.value[index] = {
        ...uploadFileList.value[index],
        status: "done",
        url: ossConfig.filePath,
        sysFileId: sysFileId // 保存系统文件ID
      }
    }

    // 更新表单数据
    addFormData.uploadFiles = uploadFileList.value.filter(item => item.status === "done")
  } catch (error) {
    console.error("文件上传失败:", error)

    // 更新文件状态为失败
    const index = uploadFileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index > -1) {
      uploadFileList.value[index] = {
        ...uploadFileList.value[index],
        status: "error",
        errorMessage: error.message
      }
    }
  } finally {
    uploadLoading.value = false
  }

  return false // 阻止默认上传行为
}

const handleDeleteFile = async file => {
  console.log("删除文件:", file)

  // 如果文件已上传成功，显示二次确认弹窗
  if (file.status === "done") {
    Modal.confirm({
      title: "确认删除文件",
      icon: createVNode(ExclamationCircleOutlined),
      content: `确定要删除文件 "${file.name}" 吗？`,
      okText: "删除",
      cancelText: "取消",
      okType: "danger",
      async onOk() {
        // 执行删除操作
        await performDeleteFile(file)
      },
      onCancel() {
        console.log("取消删除文件")
      }
    })
  } else {
    // 如果是上传失败或上传中的文件，直接删除本地记录
    await performDeleteFile(file)
  }
}

// 执行删除文件的具体操作
const performDeleteFile = async file => {
  // 如果文件已上传成功且有sysFileId，则调用删除接口
  if (file.status === "done" && file.sysFileId) {
    try {
      await deleteFile(file.sysFileId)
      console.log("服务器文件删除成功")
      message.success("文件删除成功")
    } catch (error) {
      console.error("删除服务器文件失败:", error)
      return // 如果服务器删除失败，不继续删除本地记录
    }
  }

  // 从本地文件列表中删除
  const index = uploadFileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    uploadFileList.value.splice(index, 1)
  }

  // 更新表单数据
  addFormData.uploadFiles = uploadFileList.value.filter(item => item.status === "done")
}

const fileTableColumns = [
  {
    title: "工具名称",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "状态",
    dataIndex: "status",
    key: "status"
  },
  {
    title: "操作",
    key: "action",
    width: 100
  }
]

// 响应式数据：控制年级标签的展开/收起状态
const expandedRows = ref({})

/**
 * 切换年级标签的展开/收起状态
 */
const toggleExpand = recordId => {
  expandedRows.value[recordId] = !expandedRows.value[recordId]
}

// 响应式数据：控制详情弹窗中适用年级标签的展开/收起状态
const detailExpanded = ref(false)

// 响应式数据：控制关联解决方案表格中适用年级标签的展开/收起状态
const solutionExpandedRows = ref({})

/**
 * 切换关联解决方案表格中适用年级标签的展开/收起状态
 */
const toggleSolutionExpand = recordId => {
  solutionExpandedRows.value[recordId] = !solutionExpandedRows.value[recordId]
}

// 初始化数据
onMounted(() => {
  // 获取工具包列表
  getToolkitList()
  // 获取年级数据
  getSolutionGradeListApi().then(res => {
    if (res.code === 200) {
      console.log("工具适用年级res", res)
      grade.value = (res.data || []).map(item => ({
        label: item.text,
        value: item.value
      }))
    }
  })

  // 获取对象类型数据
  getToolObjectTypeListApi().then(res => {
    if (res.code === 200) {
      console.log("工具适用对象res", res)
      toolObjectType.value = res.data || []
    }
  })
  // 获取工具适用班级类型
  getToolClassTypeListApi().then(res => {
    if (res.code === 200) {
      console.log("工具适用班级类型res", res)
      classType.value = res.data || []
    }
  })
})
</script>

<style lang="scss" scoped>
.toolkit-management {
  .search-card {
    margin-bottom: 16px;
    .marginBot {
      margin-bottom: 10px;
    }
  }

  .operation-card {
    margin-bottom: 16px;

    .table-operations {
      display: flex;
      align-items: center;
      gap: 8px;

      .selected-info {
        margin-left: 16px;
        color: #666;
        font-size: 14px;
      }
    }
  }

  // 年级选择器样式
  .grade-select-wrapper {
    .grade-select-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .selected-count {
        color: #666;
        font-size: 12px;
      }
    }
  }

  // 年级标签容器样式
  .grade-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}
:deep(.ant-upload) {
  width: 100% !important;
}
</style>
