<template>
  <div class="role-management">
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </div>

      <a-table
        :columns="columns"
        :data-source="roleList"
        :loading="loading"
        :pagination="false"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? "启用" : "禁用" }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleMenuAuth(record)">菜单授权</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" size="small" danger @click="handleDelete(record)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="角色名称" name="roleName">
          <a-input v-model:value="formData.roleName" placeholder="请输入角色名称" />
        </a-form-item>

        <a-form-item label="角色枚举值" name="roleEnum">
          <a-input v-model:value="formData.roleEnum" placeholder="请输入角色枚举值" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 菜单授权弹窗 -->
    <a-modal
      v-model:open="menuAuthVisible"
      title="菜单授权"
      width="600px"
      :confirm-loading="authSubmitLoading"
      @ok="handleMenuAuthSubmit"
      @cancel="handleMenuAuthCancel"
    >
      <div class="menu-auth-content">
        <div class="role-info">
          <span
            >当前角色：<strong>{{ currentRole?.roleName }}</strong></span
          >
        </div>
        <a-divider />
        <a-tree
          v-model:checkedKeys="checkedMenuKeys"
          :tree-data="menuTreeData"
          :field-names="{ children: 'children', title: 'title', key: 'id' }"
          checkable
          :default-expand-all="true"
          :check-strictly="false"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup name="Role">
import { PlusOutlined } from "@ant-design/icons-vue"
import {
  addRoleApi,
  editRoleApi,
  deleteRoleApi,
  getRoleListApi,
  authMenuApi,
  getMenuTreeApi,
  getRoleMenuApi
} from "@/api/index.js"

const loading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const submitLoading = ref(false)
const currentRecord = ref(null)

// 菜单授权相关
const menuAuthVisible = ref(false)
const authSubmitLoading = ref(false)
const currentRole = ref(null)
const checkedMenuKeys = ref([])

// 表单数据
const formData = reactive({
  id: null,
  roleName: "",
  roleEnum: ""
})

// 表格列配置
const columns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id"
  },
  {
    title: "角色名称",
    dataIndex: "roleName",
    key: "roleName"
  },
  {
    title: "角色枚举值",
    dataIndex: "roleEnum",
    key: "roleEnum"
  },
  {
    title: "操作",
    key: "action",
    width: 200
  }
]

// 角色列表数据（假数据）
const roleList = ref([])

// 菜单树形数据（假数据）
const menuTreeData = ref([])

// 表单验证规则
const rules = {
  roleName: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
  roleEnum: [{ required: true, message: "请输入角色枚举值", trigger: "blur" }]
}

// 获取所有相关的菜单ID（包括父级菜单）
const getAllRelatedMenuIds = selectedIds => {
  const allIds = new Set()

  const findAllParentIds = (nodes, targetId, parentIds = []) => {
    for (const node of nodes) {
      const currentParentIds = [...parentIds, node.id]

      if (node.id === targetId) {
        // 找到目标节点，将所有父级ID都加入
        currentParentIds.forEach(id => allIds.add(id))
        return true
      }

      if (node.children && node.children.length > 0) {
        if (findAllParentIds(node.children, targetId, currentParentIds)) {
          return true
        }
      }
    }
    return false
  }

  selectedIds.forEach(id => {
    findAllParentIds(menuTreeData.value, id)
  })

  return Array.from(allIds)
}

// 获取叶子节点ID（过滤掉父级节点）
const getLeafNodeIds = (allIds, treeData) => {
  const leafIds = []

  const findLeafNodes = (nodes, targetIds) => {
    nodes.forEach(node => {
      if (targetIds.includes(node.id)) {
        // 如果当前节点在目标ID列表中
        if (!node.children || node.children.length === 0) {
          // 如果是叶子节点，直接添加
          leafIds.push(node.id)
        } else {
          // 如果是父节点，检查其子节点是否都在目标列表中
          const childIds = []
          const getAllChildIds = childNodes => {
            childNodes.forEach(child => {
              childIds.push(child.id)
              if (child.children && child.children.length > 0) {
                getAllChildIds(child.children)
              }
            })
          }
          getAllChildIds(node.children)

          // 检查是否所有子节点都被授权
          const allChildrenAuthorized = childIds.every(childId => targetIds.includes(childId))

          if (!allChildrenAuthorized) {
            // 如果不是所有子节点都被授权，继续递归查找
            findLeafNodes(node.children, targetIds)
          } else {
            // 如果所有子节点都被授权，只添加叶子节点
            const getOnlyLeafIds = childNodes => {
              childNodes.forEach(child => {
                if (!child.children || child.children.length === 0) {
                  leafIds.push(child.id)
                } else {
                  getOnlyLeafIds(child.children)
                }
              })
            }
            getOnlyLeafIds(node.children)
          }
        }
      } else if (node.children && node.children.length > 0) {
        // 如果当前节点不在目标列表中，但有子节点，继续递归
        findLeafNodes(node.children, targetIds)
      }
    })
  }

  findLeafNodes(treeData, allIds)
  return [...new Set(leafIds)] // 去重
}

// 获取角色列表
const getRoleList = async () => {
  try {
    loading.value = true
    const res = await getRoleListApi()
    if (res.code === 200) {
      roleList.value = res.data || []
    } else {
      message.error(res.msg || "获取角色列表失败")
    }
  } catch (error) {
    console.error("获取角色列表失败:", error)
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  modalTitle.value = "新增"
  modalVisible.value = true
  currentRecord.value = null
  Object.assign(formData, {
    id: null,
    roleName: "",
    roleEnum: ""
  })
}

// 编辑
const handleEdit = record => {
  modalTitle.value = "编辑"
  modalVisible.value = true
  currentRecord.value = record
  Object.assign(formData, {
    id: record.id,
    roleName: record.roleName,
    roleEnum: record.roleEnum
  })
}

// 删除
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除角色 "${record.roleName}" 吗？`,
    async onOk() {
      deleteRoleApi(record.id).then(res => {
        if (res.code === 200) {
          message.success("删除成功")
          getRoleList() // 刷新列表
        } else {
          message.error(res.mag)
        }
      })
    }
  })
}

// 菜单授权
const handleMenuAuth = async record => {
  currentRole.value = record
  menuAuthVisible.value = true

  try {
    // 先获取系统所有菜单数据
    const menuRes = await getMenuTreeApi()
    if (menuRes.code === 200) {
      console.log("全量菜单数据", menuRes.data)
      menuTreeData.value = menuRes.data

      // 菜单数据加载完成后，再获取当前角色的已绑定菜单
      const roleMenuRes = await getRoleMenuApi(record.roleEnum)
      if (roleMenuRes.code === 200) {
        const bindIds = (roleMenuRes.data || []).map(item => item.id)
        console.log("当前角色已授权菜单ids", bindIds)

        // 过滤出叶子节点ID，用于回显
        const leafNodeIds = getLeafNodeIds(bindIds, menuTreeData.value)
        console.log("过滤后的叶子节点ids", leafNodeIds)

        // 将过滤后的叶子节点ID设置为选中状态
        checkedMenuKeys.value = leafNodeIds
      } else {
        // 如果获取角色菜单失败，清空选中状态
        checkedMenuKeys.value = []
      }
    } else {
      message.error(menuRes.msg || "获取菜单数据失败")
    }
  } catch (error) {
    console.error("获取菜单授权数据失败:", error)
    // 出错时清空选中状态
    checkedMenuKeys.value = []
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    // return
    if (formData.id) {
      // 编辑 - 更新现有数据
      editRoleApi(formData).then(res => {
        if (res.code === 200) {
          message.success("编辑成功")
          getRoleList() // 刷新列表
        } else {
          message.error(res.msg)
        }
      })
    } else {
      // 新增 - 添加新数据
      const params = {
        roleName: formData.roleName,
        roleEnum: formData.roleEnum
      }
      addRoleApi(params).then(res => {
        if (res.code === 200) {
          message.success("新增成功")
          getRoleList() // 刷新列表
        } else {
          message.error(res.msg)
        }
      })
    }

    modalVisible.value = false
    submitLoading.value = false
  } catch (error) {
    console.log("表单验证失败:", error)
    submitLoading.value = false
  }
}

// 菜单授权提交
const handleMenuAuthSubmit = async () => {
  try {
    authSubmitLoading.value = true
    // 获取所有相关的菜单ID（包括父级菜单）
    const allRelatedMenuIds = getAllRelatedMenuIds(checkedMenuKeys.value)
    console.log("所有选中的id集合", allRelatedMenuIds)

    const res = await authMenuApi(currentRole.value.roleEnum, allRelatedMenuIds)
    if (res.code === 200) {
      message.success("授权成功")
      menuAuthVisible.value = false
      getRoleList() // 刷新列表
    } else {
      message.error(res.msg || "授权失败")
    }
  } catch (error) {
    console.error("菜单授权失败:", error)
    // message.error("授权失败")
  } finally {
    authSubmitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  submitLoading.value = false
}

const handleMenuAuthCancel = () => {
  menuAuthVisible.value = false
  authSubmitLoading.value = false
}

// 组件挂载时初始化数据
onMounted(() => {
  getRoleList()
})
</script>

<style lang="scss" scoped>
.role-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}

.menu-auth-content {
  .role-info {
    padding: 8px 0;
    font-size: 14px;
    color: #666;
  }
}
</style>
