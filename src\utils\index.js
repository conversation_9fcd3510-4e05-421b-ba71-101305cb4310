import Layout from "@/layout/index.vue"

// 静态导入映射 - Vite需要这种方式
const componentMap = {
  // 系统管理模块
  "system/role": () => import("@/views/system/role/index.vue"),
  "system/menu": () => import("@/views/system/menu/index.vue"),
  "system/cloudschool": () => import("@/views/system/cloudschool/index.vue"),
  "system/tasktype": () => import("@/views/system/tasktype/index.vue"),
  "system/dic": () => import("@/views/system/dic/index.vue"),
  "system/manager": () => import("@/views/system/manager/index.vue"),
  "system/fund": () => import("@/views/system/fund/index.vue"),
  "system/minister": () => import("@/views/system/minister/index.vue"),
  "system/banner": () => import("@/views/system/banner/index.vue"),
  "system/metmanage": () => import("@/views/system/metmanage/index.vue"),
  // 运营管理模块
  "opsmanage/scholl": () => import("@/views/opsmanage/scholl/index.vue"),
  "opsmanage/class": () => import("@/views/opsmanage/class/index.vue"),
  "opsmanage/learnoffice": () => import("@/views/opsmanage/learnoffice/index.vue"),

  // 解决方案管理模块
  "solutionmanage/toolkit": () => import("@/views/solutionmanage/toolkit/index.vue"),
  "solutionmanage/solution": () => import("@/views/solutionmanage/solution/index.vue")
}

/**
 * 转换后端路由数据为前端路由格式
 * @param {Array} routes 后端路由数据
 * @returns {Array} 前端路由格式
 */
export const transformRoutes = routes => {
  const transformedRoutes = []

  routes.forEach(route => {
    // 如果是Layout组件，需要处理其子路由
    if (route.component === "Layout") {
      const layoutRoute = {
        path: route.path,
        name: route.title || route.name,
        component: Layout,
        meta: route.meta,
        redirect: route.redirect
      }

      // 处理子路由
      if (route.children && route.children.length > 0) {
        layoutRoute.children = route.children.map(child => {
          // 根据父路由路径确定组件映射键
          let componentKey = ""

          if (route.path === "/system") {
            componentKey = `system/${child.component}`
          } else if (route.path === "/opsmanage") {
            componentKey = `opsmanage/${child.component}`
          } else if (route.path === "/solutionmanage") {
            componentKey = `solutionmanage/${child.component}`
          } else {
            componentKey = `system/${child.component}` // 默认
          }

          // 从映射中获取组件导入函数
          const componentImport = componentMap[componentKey]

          if (!componentImport) {
            console.warn(`未找到组件映射: ${componentKey}`)
            // 回退到默认组件或错误页面
            return {
              path: child.path,
              name: child.title || child.name,
              component: () => import("@/views/404.vue"),
              meta: child.meta
            }
          }

          return {
            path: child.path,
            name: child.title || child.name,
            component: componentImport,
            meta: child.meta
          }
        })
      }

      transformedRoutes.push(layoutRoute)
    } else {
      // 处理其他类型的路由
      const transformedRoute = {
        path: route.path,
        name: route.title || route.name,
        meta: route.meta,
        redirect: route.redirect
      }

      // 根据路径确定组件映射键
      let componentKey = ""
      if (route.path.startsWith("/system")) {
        componentKey = `system/${route.component}`
      } else if (route.path.startsWith("/opsmanage")) {
        componentKey = `opsmanage/${route.component}`
      } else if (route.path.startsWith("/solutionmanage")) {
        componentKey = `solutionmanage/${route.component}`
      } else {
        componentKey = route.component
      }

      const componentImport = componentMap[componentKey]

      if (!componentImport) {
        console.warn(`未找到组件映射: ${componentKey}`)
        transformedRoute.component = () => import("@/views/404.vue")
      } else {
        transformedRoute.component = componentImport
      }

      // 处理子路由
      if (route.children && route.children.length > 0) {
        transformedRoute.children = transformRoutes(route.children)
      }

      transformedRoutes.push(transformedRoute)
    }
  })

  return transformedRoutes
}

/**
 * 动态添加路由
 * @param {Object} router 路由实例
 * @param {Array} routes 路由数据
 */
export const addDynamicRoutes = (router, routes) => {
  const transformedRoutes = transformRoutes(routes)

  // 先移除可能存在的404路由
  if (router.hasRoute("NotFound")) {
    router.removeRoute("NotFound")
  }

  // 添加动态路由
  transformedRoutes.forEach(route => {
    router.addRoute(route)
  })

  // 最后添加404路由
  router.addRoute({
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/404.vue"),
    meta: { title: "页面未找到" }
  })

  return transformedRoutes
}

/**
 * 检查路由是否已经添加
 * @param {Object} router 路由实例
 * @returns {Boolean} 是否已添加动态路由
 */
export const hasAddedRoutes = router => {
  const routes = router.getRoutes()
  // 检查是否存在主要的模块路由
  return routes.some(
    route =>
      route.path === "/system" ||
      route.path === "/opsmanage" ||
      route.path === "/solutionmanage" ||
      route.name === "系统管理" ||
      route.name === "运营管理" ||
      route.name === "解决方案管理"
  )
}
