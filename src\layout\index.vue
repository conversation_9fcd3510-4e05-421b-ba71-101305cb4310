<template>
  <a-layout class="layout">
    <!-- 左边内容（侧边栏） -->
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible class="sider">
      <div class="logo">
        <h2 v-if="!collapsed">劝学管理后台</h2>
        <h2 v-else style="font-size: 18px">劝学</h2>
      </div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="light"
        :items="menuItems"
        @click="handleMenuClick"
      />
    </a-layout-sider>
    <!-- 右边内容（顶部导航、内容区域）-->
    <a-layout>
      <!-- 顶部导航 -->
      <a-layout-header class="header">
        <!-- 左侧点击收起、面包屑 -->
        <div class="header-left">
          <menu-unfold-outlined
            v-if="collapsed"
            class="trigger"
            @click="() => (collapsed = !collapsed)"
          />
          <menu-fold-outlined v-else class="trigger" @click="() => (collapsed = !collapsed)" />
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
              {{ item.title }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        <!-- 右侧退出登录 -->
        <div class="header-right">
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              <a-avatar style="background-color: #87d068">
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <span class="username">{{ userStore.userInfo.name }}</span>
              <!-- <down-outlined /> -->
            </a>
            <template #overlay>
              <a-menu>
                <!-- <a-menu-item key="profile">
                  <user-outlined />
                  个人中心
                </a-menu-item>
                <a-menu-item key="settings">
                  <setting-outlined />
                  设置
                </a-menu-item> -->
                <!-- <a-menu-divider /> -->
                <a-menu-item key="logout" @click="handleLogout">
                  <logout-outlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>

      <!-- 内容区域 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, computed, watch, h } from "vue"
import { useRouter, useRoute } from "vue-router"
import {
  NodeCollapseOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  LogoutOutlined,
  // 搜索图标
  SearchOutlined,
  GroupOutlined,
  MehOutlined,
  ReconciliationOutlined,
  PlayCircleOutlined,
  // 方向性图标
  DownOutlined,
  UpOutlined,
  LeftOutlined,
  RightOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  LeftCircleOutlined,
  RightCircleOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
  ForwardOutlined,
  BackwardOutlined,
  EnterOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  LoginOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,

  // 提示建议性图标
  QuestionOutlined,
  QuestionCircleOutlined,
  PlusOutlined,
  PlusCircleOutlined,
  PauseOutlined,
  PauseCircleOutlined,
  MinusOutlined,
  MinusCircleOutlined,
  InfoOutlined,
  InfoCircleOutlined,
  ExclamationOutlined,
  ExclamationCircleOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined,

  // 编辑类图标
  EditOutlined,
  FormOutlined,
  CopyOutlined,
  DeleteOutlined,
  SnippetsOutlined,
  HighlightOutlined,
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  RedoOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,

  // 数据类图标
  AreaChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  DotChartOutlined,
  LineChartOutlined,
  RadarChartOutlined,
  FallOutlined,
  RiseOutlined,
  StockOutlined,
  FundOutlined,
  SlidersOutlined,

  // 品牌和标识
  AndroidOutlined,
  AppleOutlined,
  WindowsOutlined,
  ChromeOutlined,
  GithubOutlined,
  WechatOutlined,
  AlipayOutlined,
  TaobaoOutlined,
  QqOutlined,
  DingdingOutlined,
  AntDesignOutlined,
  GoogleOutlined,
  FacebookOutlined,
  LinkedinOutlined,
  InstagramOutlined,
  SkypeOutlined,
  SlackOutlined,
  ZhihuOutlined,

  // 应用类图标
  HomeOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  DashboardOutlined,
  AppstoreOutlined,
  MenuOutlined,
  BarsOutlined,
  MailOutlined,
  BellOutlined,
  CalendarOutlined,
  FileOutlined,
  FolderOutlined,
  PictureOutlined,
  CameraOutlined,
  VideoCameraOutlined,
  MobileOutlined,
  TabletOutlined,
  LaptopOutlined,
  DesktopOutlined,
  PrinterOutlined,
  ScanOutlined,
  CloudOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  WifiOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  SafetyOutlined,
  BankOutlined,
  ShopOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  CreditCardOutlined,
  WalletOutlined,
  GiftOutlined,
  TrophyOutlined,
  CrownOutlined,
  HeartOutlined,
  StarOutlined,
  LikeOutlined,
  SmileOutlined,
  FireOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BookOutlined,
  ReadOutlined,
  BulbOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  BuildOutlined,
  BugOutlined,
  CodeOutlined,
  ApiOutlined,
  LinkOutlined,
  ShareAltOutlined,
  SendOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  SyncOutlined,
  ReloadOutlined,
  LoadingOutlined,
  PoweroffOutlined,
  HistoryOutlined,
  ScheduleOutlined,
  CarryOutOutlined,
  AuditOutlined,
  ProfileOutlined,
  ProjectOutlined,
  SolutionOutlined,
  TableOutlined,
  LayoutOutlined,
  FilterOutlined,
  TagOutlined,
  TagsOutlined,
  FlagOutlined,
  CompassOutlined,
  EnvironmentOutlined,
  AimOutlined,
  PushpinOutlined
} from "@ant-design/icons-vue"
import { useUserStore } from "@/stores/user"
const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

const collapsed = ref(false)
const selectedKeys = ref([])
const openKeys = ref([])
// 监听 localStorage 事件
window.addEventListener("storage", function (event) {
  // event 对象包含了变化的信息
  console.log("Storage event triggered", event)
})
// 菜单配置
const menuItems = ref([
  {
    key: "/dashboard",
    icon: () => h(DashboardOutlined),
    label: "仪表盘",
    title: "仪表盘"
  }
  // {
  //   key: "/system",
  //   icon: () => h(SettingOutlined),
  //   label: "系统管理",
  //   title: "系统管理",
  //   children: [
  //     {
  //       key: "/system/role",
  //       icon: () => h(TeamOutlined),
  //       label: "角色管理",
  //       title: "角色管理"
  //     },
  //     {
  //       key: "/system/menu",
  //       icon: () => h(MenuOutlined),
  //       label: "菜单管理",
  //       title: "菜单管理"
  //     },
  //     {
  //       key: "/system/cloudschool",
  //       icon: () => h(HistoryOutlined),
  //       label: "云校管理",
  //       title: "云校管理"
  //     },
  //     {
  //       key: "/system/tasktype",
  //       icon: () => h(GroupOutlined),
  //       label: "任务类型管理",
  //       title: "任务类型管理"
  //     },
  //     {
  //       key: "/system/dic",
  //       icon: () => h(MehOutlined),
  //       label: "参数字典管理",
  //       title: "参数字典管理"
  //     },
  //     {
  //       key: "/system/manager",
  //       icon: () => h(MehOutlined),
  //       label: "管理员管理",
  //       title: "管理员管理"
  //     }
  //   ]
  // }
])

// 面包屑导航
const breadcrumbItems = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta.title
  }))
})

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  newPath => {
    selectedKeys.value = [newPath]
    // 设置展开的菜单
    if (newPath.startsWith("/system")) {
      openKeys.value = ["/system"]
    }
  },
  { immediate: true }
)

// 菜单点击事件
const handleMenuClick = ({ key }) => {
  router.push(key)
}

// 退出登录
const handleLogout = () => {
  localStorage.clear()
  router.push("/login")
}
//处理菜单数据
const handleMenu = () => {
  if (localStorage.getItem("userMenu")) {
    const menu = JSON.parse(localStorage.getItem("userMenu"))

    // 图标映射
    const iconMap = {
      NodeCollapseOutlined,
      MenuFoldOutlined,
      // 搜索图标
      SearchOutlined,
      GroupOutlined,
      MehOutlined,
      ReconciliationOutlined,
      PlayCircleOutlined,
      // 方向性图标
      DownOutlined,
      UpOutlined,
      LeftOutlined,
      RightOutlined,
      CaretUpOutlined,
      CaretDownOutlined,
      CaretLeftOutlined,
      CaretRightOutlined,
      UpCircleOutlined,
      DownCircleOutlined,
      LeftCircleOutlined,
      RightCircleOutlined,
      DoubleRightOutlined,
      DoubleLeftOutlined,
      ForwardOutlined,
      BackwardOutlined,
      EnterOutlined,
      ArrowUpOutlined,
      ArrowDownOutlined,
      ArrowLeftOutlined,
      ArrowRightOutlined,
      LoginOutlined,
      FullscreenOutlined,
      FullscreenExitOutlined,

      // 提示建议性图标
      QuestionOutlined,
      QuestionCircleOutlined,
      PlusOutlined,
      PlusCircleOutlined,
      PauseOutlined,
      PauseCircleOutlined,
      MinusOutlined,
      MinusCircleOutlined,
      InfoOutlined,
      InfoCircleOutlined,
      ExclamationOutlined,
      ExclamationCircleOutlined,
      CloseOutlined,
      CloseCircleOutlined,
      CheckOutlined,
      CheckCircleOutlined,
      ClockCircleOutlined,
      WarningOutlined,
      StopOutlined,

      // 编辑类图标
      EditOutlined,
      FormOutlined,
      CopyOutlined,
      DeleteOutlined,
      SnippetsOutlined,
      HighlightOutlined,
      AlignCenterOutlined,
      AlignLeftOutlined,
      AlignRightOutlined,
      BoldOutlined,
      ItalicOutlined,
      UnderlineOutlined,
      RedoOutlined,
      UndoOutlined,
      ZoomInOutlined,
      ZoomOutOutlined,
      FontColorsOutlined,
      FontSizeOutlined,
      SortAscendingOutlined,
      SortDescendingOutlined,
      OrderedListOutlined,
      UnorderedListOutlined,

      // 数据类图标
      AreaChartOutlined,
      PieChartOutlined,
      BarChartOutlined,
      DotChartOutlined,
      LineChartOutlined,
      RadarChartOutlined,
      FallOutlined,
      RiseOutlined,
      StockOutlined,
      FundOutlined,
      SlidersOutlined,

      // 品牌和标识
      AndroidOutlined,
      AppleOutlined,
      WindowsOutlined,
      ChromeOutlined,
      GithubOutlined,
      WechatOutlined,
      AlipayOutlined,
      TaobaoOutlined,
      QqOutlined,
      DingdingOutlined,
      AntDesignOutlined,
      GoogleOutlined,
      FacebookOutlined,
      LinkedinOutlined,
      InstagramOutlined,
      SkypeOutlined,
      SlackOutlined,
      ZhihuOutlined,

      // 应用类图标
      HomeOutlined,
      SettingOutlined,
      UserOutlined,
      TeamOutlined,
      DashboardOutlined,
      AppstoreOutlined,
      MenuOutlined,
      BarsOutlined,
      MailOutlined,
      BellOutlined,
      CalendarOutlined,
      FileOutlined,
      FolderOutlined,
      PictureOutlined,
      CameraOutlined,
      VideoCameraOutlined,
      MobileOutlined,
      TabletOutlined,
      LaptopOutlined,
      DesktopOutlined,
      PrinterOutlined,
      ScanOutlined,
      CloudOutlined,
      DatabaseOutlined,
      GlobalOutlined,
      WifiOutlined,
      LockOutlined,
      UnlockOutlined,
      KeyOutlined,
      SafetyOutlined,
      BankOutlined,
      ShopOutlined,
      ShoppingOutlined,
      ShoppingCartOutlined,
      CreditCardOutlined,
      WalletOutlined,
      GiftOutlined,
      TrophyOutlined,
      CrownOutlined,
      HeartOutlined,
      StarOutlined,
      LikeOutlined,
      SmileOutlined,
      FireOutlined,
      EyeOutlined,
      EyeInvisibleOutlined,
      BookOutlined,
      ReadOutlined,
      BulbOutlined,
      RocketOutlined,
      ThunderboltOutlined,
      ToolOutlined,
      BuildOutlined,
      BugOutlined,
      CodeOutlined,
      ApiOutlined,
      LinkOutlined,
      ShareAltOutlined,
      SendOutlined,
      UploadOutlined,
      DownloadOutlined,
      ImportOutlined,
      ExportOutlined,
      SyncOutlined,
      ReloadOutlined,
      LoadingOutlined,
      PoweroffOutlined,
      HistoryOutlined,
      ScheduleOutlined,
      CarryOutOutlined,
      AuditOutlined,
      ProfileOutlined,
      ProjectOutlined,
      SolutionOutlined,
      TableOutlined,
      LayoutOutlined,
      FilterOutlined,
      TagOutlined,
      TagsOutlined,
      FlagOutlined,
      CompassOutlined,
      EnvironmentOutlined,
      AimOutlined,
      PushpinOutlined,
      MenuUnfoldOutlined,
      LogoutOutlined
    }

    // 递归转换菜单数据
    const transformMenuData = menuData => {
      return menuData.map(item => {
        const menuItem = {
          key: item.path,
          label: item.meta?.title || item.title,
          title: item.meta?.title || item.title
        }

        // 设置图标
        const iconName = item.meta?.icon || item.icon
        if (iconName && iconMap[iconName]) {
          menuItem.icon = () => h(iconMap[iconName])
        }

        // 处理子菜单
        if (item.children && item.children.length > 0) {
          menuItem.children = item.children.map(child => ({
            key: `${item.path}/${child.path}`,
            label: child.meta?.title || child.title,
            title: child.meta?.title || child.title,
            icon:
              child.meta?.icon && iconMap[child.meta.icon]
                ? () => h(iconMap[child.meta.icon])
                : undefined
          }))
        }

        return menuItem
      })
    }
    // 转换并更新菜单数据
    menuItems.value = [...menuItems.value, ...transformMenuData(menu)]
  }
}

onMounted(() => {
  handleMenu()
})
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;
}

.sider {
  background: #fff;
  :deep(.ant-layout-sider-children) {
    height: 100vh;
    overflow-y: auto;
  }
  .logo {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f2f5;
    margin: 16px;
    border-radius: 6px;
    border: 1px solid #d9d9d9;

    h2 {
      color: #1890ff;
      margin: 0;
      font-weight: bold;
    }
  }
}

.header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .header-left {
    display: flex;
    align-items: center;

    .trigger {
      font-size: 18px;
      line-height: 64px;
      // padding: 0 24px;
      cursor: pointer;
      transition: color 0.3s;

      &:hover {
        color: #1890ff;
      }
    }

    .breadcrumb {
      margin-left: 16px;
    }
  }

  .header-right {
    .ant-dropdown-link {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.65);
      text-decoration: none;

      .username {
        margin: 0 8px;
      }

      &:hover {
        color: #1890ff;
      }
    }
  }
}

.content {
  margin: 20px;
  overflow-x: hidden;

  .content-wrapper {
    height: 100%;
    background: #fff;
    padding: 24px;
    border-radius: 6px;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.12),
      0 1px 2px rgba(0, 0, 0, 0.24);
    min-height: calc(100vh - 112px);
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
  }
}
</style>
