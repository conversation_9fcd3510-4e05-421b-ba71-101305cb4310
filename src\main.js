import { createApp } from "vue"
import { createPinia } from "pinia"
import "ant-design-vue/dist/reset.css"
import "./style/index.scss"
import piniaPluginPersistedstate from "pinia-plugin-persistedstate"
import App from "./App.vue"
import router from "./router"

// 配置 dayjs 中文语言包
import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
dayjs.locale("zh-cn")

const app = createApp(App)
const pinia = createPinia()

pinia.use(piniaPluginPersistedstate)
app.use(pinia)
app.use(router)

app.mount("#app")
