# 自动导入配置说明

## 🚀 已配置的自动导入功能

本项目已经配置了 `unplugin-auto-import` 和 `unplugin-vue-components` 插件，实现了以下自动导入功能：

### 1. Vue 3 API 自动导入
以下 Vue 3 API 无需手动导入，可直接使用：

```javascript
// ✅ 无需导入，直接使用
const count = ref(0)
const user = reactive({ name: 'John' })
const doubleCount = computed(() => count.value * 2)

watch(count, (newVal) => {
  console.log('count changed:', newVal)
})

onMounted(() => {
  console.log('组件已挂载')
})

onUnmounted(() => {
  console.log('组件即将卸载')
})
```

**支持的 Vue API：**
- `ref`, `reactive`, `computed`, `readonly`
- `watch`, `watchEffect`
- `onMounted`, `onUnmounted`, `onUpdated`, `onBeforeMount`, `onBeforeUnmount`, `onBeforeUpdate`
- `provide`, `inject`
- `nextTick`
- `defineProps`, `defineEmits`, `defineExpose`
- 等等...

### 2. Vue Router API 自动导入
```javascript
// ✅ 无需导入，直接使用
const router = useRouter()
const route = useRoute()

// 路由跳转
router.push('/dashboard')

// 获取路由参数
const id = route.params.id
```

### 3. Pinia API 自动导入
```javascript
// ✅ 无需导入，直接使用
const userStore = useUserStore()
```

### 4. Ant Design Vue API 自动导入
以下常用的 Ant Design Vue API 无需手动导入：

```javascript
// ✅ 无需导入，直接使用
message.success('操作成功')
message.error('操作失败')

Modal.confirm({
  title: '确认删除',
  content: '确定要删除吗？',
  onOk() {
    // 删除逻辑
  }
})

notification.open({
  message: '通知标题',
  description: '通知内容'
})
```

### 5. Ant Design Vue 组件自动导入
所有 Ant Design Vue 组件无需手动导入，可直接在模板中使用：

```vue
<template>
  <!-- ✅ 无需导入，直接使用 -->
  <a-button type="primary">按钮</a-button>
  <a-input v-model:value="inputValue" />
  <a-table :columns="columns" :data-source="data" />
  <a-form :model="form">
    <a-form-item>
      <a-input />
    </a-form-item>
  </a-form>
</template>
```

## 📁 生成的文件

配置完成后，会自动生成以下文件：

- `auto-imports.d.ts` - Vue API 类型声明文件
- `components.d.ts` - 组件类型声明文件
- `.eslintrc-auto-import.json` - ESLint 配置文件

这些文件会自动更新，无需手动维护。

## 🔧 配置文件

自动导入的配置位于 `vite.config.js`：

```javascript
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig({
  plugins: [
    vue(),
    // 自动导入Vue3 API
    AutoImport({
      imports: [
        'vue',
        'vue-router',
        'pinia',
        {
          'ant-design-vue': [
            'message',
            'Modal',
            'notification'
          ]
        }
      ],
      dts: true,
      eslintrc: {
        enabled: true,
      },
    }),
    // 自动导入组件
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false,
        }),
      ],
      dts: true,
    }),
  ],
})
```

## 📝 使用示例

### 之前（需要手动导入）
```vue
<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const count = ref(0)
const router = useRouter()

onMounted(() => {
  message.success('页面加载完成')
})
</script>
```

### 现在（自动导入）
```vue
<script setup>
// 无需任何导入语句！

const count = ref(0)
const router = useRouter()

onMounted(() => {
  message.success('页面加载完成')
})
</script>
```

## 🎯 优势

1. **减少样板代码** - 无需重复的导入语句
2. **提高开发效率** - 专注于业务逻辑而非导入管理
3. **自动类型支持** - TypeScript 类型自动生成
4. **按需加载** - 只导入实际使用的API和组件
5. **IDE支持** - 完整的智能提示和自动补全

## 🚨 注意事项

1. 如果需要使用未配置的API，仍需手动导入
2. 图标组件仍需手动导入（如 `@ant-design/icons-vue`）
3. 第三方库的API需要手动导入
4. 建议在团队中统一使用，避免混淆

## 📚 更多配置

如需添加更多自动导入的API，可以在 `vite.config.js` 中的 `AutoImport` 配置中添加：

```javascript
AutoImport({
  imports: [
    'vue',
    'vue-router',
    'pinia',
    {
      'lodash-es': ['debounce', 'throttle'],
      'dayjs': [['default', 'dayjs']],
    }
  ],
})
``` 