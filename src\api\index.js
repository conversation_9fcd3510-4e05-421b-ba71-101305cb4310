import request from "./request"
import qs from "qs"

// 通用的qs序列化配置，数组参数转换为 name=1&name=2 格式
const stringifyParams = params => {
  return qs.stringify(params, {
    arrayFormat: "repeat" // 数组参数格式：name=1&name=2
  })
}
/**
 * 登录接口
 * @param {*} data
 * @returns
 */
export const loginApi = data => request.post("/api/v1/login", data)

/**
 * 获取用户菜单权限
 * @param {*} data
 * @returns
 */
export const getUserMenuApi = () => request.get("/api/v1/role/menus")

/**
 * 获取年级枚举
 */
export const getGradeDataApi = () => request.get("/api/v1/public/grade")
/**
 * 获取科目枚举信息
 */
export const getSubjectsListApi = () => request.get("/api/v1/public/subjects")

// 云校管理相关接口.........................................................................
/**
 * 获取列表
 * @param {*} data
 * @returns
 */
export const getCloudSchoolListApi = data =>
  request.get(`/api/v1/cloud-school/page?${stringifyParams(data)}`)

/**
 * 添加
 * @param {*} data
 * @returns
 */
export const addCloudSchoolApi = data => request.post("/api/v1/cloud-school", data)

/**
 * 更新
 * @param {*} data
 * @returns
 */
export const editCloudSchoolApi = data => request.put("/api/v1/cloud-school", data)

/**
 * 删除
 * @param {*} id
 * @returns
 */
export const deleteCloudSchoolApi = data => request.delete(`/api/v1/cloud-school`, { data })

/**
 * 设置云学校的学校关系
 * @param {*} data
 * @returns
 */
export const deleteClou3dSchoolApi = data => request.post(`/api/v1/cloud-school/schools`, data)

/**
 * 获取云校数据
 * @param {*} id
 * @returns
 */
export const getCloudSchoolDropdownListApi = () => request.get(`/api/v1/cloud-school/combox`)

/**
 * 获取学校数据
 * @param {*} id
 * @returns
 */
export const getSchoolDropdownListApi = data =>
  request.get(`/api/v1/school/combox?${stringifyParams(data)}`)
/**
 * 编辑时查询单个云校数据
 * @returns
 */
export const getCloudSchoolDetailApi = id => request.get(`/api/v1/cloud-school/${id}`)

// 任务类型管理相关接口.........................................................................
/**
 * 添加任务类型
 * @param {*} data
 * @returns
 */
export const addTaskTypeApi = data => request.post("/api/v1/task-type", data)
/**
 * 更新任务类型
 * @param {*} data
 * @returns
 */
export const editTaskTypeApi = data => request.put("/api/v1/task-type", data)
/**
 * 删除任务类型
 * @param {*} data
 * @returns
 */
export const deleteTaskTypeApi = id => request.delete(`/api/v1/task-type/${id}`)
/**
 * 获取任务管理列表
 * @param {*} data
 * @returns
 */
export const getTaskTypeListApi = data => request.get("/api/v1/task-type", data)

// 参数字典管理相关接口.........................................................................

/**
 * 添加字典
 * @param {*} data
 * @returns
 */
export const addDicApi = data => request.post("/api/v1/dic", data)
/**
 * 更新字典
 * @param {*} data
 * @returns
 */
export const editDicApi = data => request.put("/api/v1/dic", data)
/**
 * 删除字典
 * @param {*} id
 * @returns
 */
export const deleteDicApi = id => request.delete(`/api/v1/dic/${id}`)
/**
 * 获取分页列表
 * @param {*} data
 * @returns
 */
export const getDicListApi = data => request.get(`/api/v1/dic/page?${stringifyParams(data)}`)
/**
 * 获取字典树
 * @param {*} id
 * @returns
 */
export const getDicTreeApi = id => request.get(`/api/v1/dic/child?id=${id}`)

/**
 * 获取单个字典详情
 * @param {*} id
 * @returns
 */
export const getDicDetailApi = id => request.get(`/api/v1/dic/${id}`)
// 管理员管理相关接口.........................................................................

/**
 * 获取管理员列表
 * @param {*} data
 * @returns
 */
export const getManagerListApi = data => request.get(`/api/v1/admin/page?${stringifyParams(data)}`)

/**
 * 添加管理员
 * @param {*} data
 * @returns
 */
export const addManagerApi = data => request.post("/api/v1/admin", data)

/**
 * 更新管理员
 * @param {*} data
 * @returns
 */
export const editManagerApi = data => request.put("/api/v1/admin", data)

/**
 * 删除管理员
 * @param {*} id
 * @returns
 */
export const deleteManagerApi = data => request.delete(`/api/v1/admin`, { data })

/**
 * 更改管理员密码
 * @param {*} data
 * @returns
 */
export const changePasswordApi = data => request.put("/api/v1/admin/pwd", data)

/**
 * 获取管理员详情
 * @param {*} id
 * @returns
 */
export const getManagerDetailApi = id => request.get(`/api/v1/admin/${id}`)

// 菜单管理相关接口.........................................................................

/**
 * 添加菜单
 * @param {*} data
 * @returns
 */
export const addMenuApi = data => request.post(`/api/v1/menu`, data)

/**
 * 更新菜单
 * @param {*} data
 * @returns
 */
export const editMenuApi = data => request.put(`/api/v1/menu`, data)

/**
 * 删除菜单
 * @param {*} id
 * @returns
 */
export const deleteMenuApi = id => request.delete(`/api/v1/menu/${id}`)

/**
 * 获取菜单详情
 * @param {*} id
 * @returns
 */
export const getMenuDetailApi = id => request.get(`/api/v1/menu/${id}`)

/**
 * 获取菜单树
 * @param {*} id
 * @returns
 */
export const getMenuTreeApi = (id = 0) => request.get(`/api/v1/menu/child?id=${id}`)

// 经费管理相关接口.........................................................................
/**
 * 新增
 */
export const addFinancialApi = data => request.post("/api/v1/financial", data)

/**
 * 删除
 */
export const deleteFinancialApi = data => request.delete("/api/v1/financial", { data })

/**
 * 更新经费指标信息
 * @param {*} data
 * @returns
 */
export const editFinancialApi = data => request.put("/api/v1/financial", data)

/**
 * 获取经费指标列表
 * @param {*} data
 * @returns
 */
export const getFinancialListApi = data =>
  request.get(`/api/v1/financial/page?${stringifyParams(data)}`)

/**
 * 获取单个经费指标信息
 * @param {*} data
 * @returns
 */
export const getSingleFinancialApi = id => request.get(`/api/v1/financial/${id}`)

// 角色管理相关接口...........................................................................

/**
 * 添加角色
 * @param {*} data
 * @returns
 */
export const addRoleApi = data => request.post(`/api/v1/role`, data)

/**
 * 更新角色
 * @param {*} data
 * @returns
 */
export const editRoleApi = data => request.put(`/api/v1/role`, data)

/**
 * 删除角色
 * @param {*} data
 * @returns
 */
export const deleteRoleApi = data => request.delete(`/api/v1/role`, { data })

/**
 * 获取角色列表
 * @returns
 */
export const getRoleListApi = () => request.get(`/api/v1/role`)

/**
 * 授权菜单
 * @param {*} data
 * @returns
 */
export const authMenuApi = (roleEnum, data) =>
  request.post(`/api/v1/role/auth-menu?roleEnum=${roleEnum}`, data)

/**
 * 获取当前角色已授权菜单
 * @param {*} data
 * @returns
 */
export const getRoleMenuApi = id => request.get(`/api/v1/role/auth-menu?roleEnum=${id}`)

// 学校管理相关接口...........................................................................
/**
 * 添加
 * @param {*} data
 * @returns
 */
export const addSchoolApi = data => request.post(`/api/v1/school`, data)

/**
 * 更新
 * @param {*} data
 * @returns
 */
export const editSchoolApi = data => request.put(`/api/v1/school`, data)

/**
 * 删除
 * @param {*} data
 * @returns
 */
export const deleteSchoolApi = data => request.delete(`/api/v1/school`, { data })

/**
 * 获取列表
 * @param {*} data
 * @returns
 */
export const getSchoolListApi = data => request.get(`/api/v1/school/page?${stringifyParams(data)}`)

/**
 * 查询单个学校信息
 * @param {*} data
 * @returns
 */
export const getSchoolDetailApi = id => request.get(`/api/v1/school/${id}`)

/**
 * 省级列表
 * @param {*} data
 * @returns
 */
export const getproviceApi = () => request.get(`/api/v1/school/provice`)

/**
 * 市级列表
 * @param {*} data
 * @returns
 */
export const getCityApi = id => request.get(`/api/v1/school/city?pid=${id}`)

/**
 * 区级列表
 * @param {*} data
 * @returns
 */
export const getRegionApi = id => request.get(`/api/v1/school/region?cid=${id}`)

// 班级管理相关接口...........................................................................

/**
 * 获取学校年级列表
 * @param {*} schoolId
 * @returns
 */
export const getGradeListApi = schoolId => request.get(`/api/v1/classes/grade?schoolId=${schoolId}`)

/**
 * 获取学校班级列表
 * @param {*} data
 * @returns
 */
export const getClassListApi = data => request.get(`/api/v1/classes?${stringifyParams(data)}`)

/**
 * 添加班级
 * @param {*} data
 * @returns
 */
export const addClassListApi = data => request.post(`/api/v1/classes`, [data])

/**
 * 更新班级
 * @param {*} data
 * @returns
 */
export const editClassListApi = data => request.put(`/api/v1/classes`, data)

/**
 * 删除班级
 * @param {*} data
 * @returns
 */
export const deleteClassListApi = data => request.delete(`/api/v1/classes`, { data })

/**
 * 获取班级科目教师列表
 * @param {*} classesId
 * @returns
 */
export const getTeacherListApi = classesId =>
  request.get(`/api/v1/classes/teacher?classesId=${classesId}`)

/**
 * 添加教师
 * {
  "classId": 0,
  "teacherName": "string",
  "subId": 0
}
 */
export const addTeacherApi = data => request.post(`/api/v1/classes/teacher`, data)

/**
 * 删除老师
 * @returns
 */
export const deleteTeacherApi = id => request.delete(`/api/v1/classes/teacher?id=${id}`)

/**
 * 获取班级学生列表
 * @param {*} data
 * @returns
 */
export const getStudentsListApi = data =>
  request.get(`/api/v1/classes/students?${stringifyParams(data)}`)

/**
 * 获取班级学习官
 * @param {*} classesId
 * @returns
 */
export const getLearnOfficeApi = classesId =>
  request.get(`/api/v1/classes/learn-office?classesId=${classesId}`)

/**
 * 新增或编辑班级学习官
 * @param {*} data
 * @returns
 */
export const addOrUpdateLearnOfficeApi = data => request.post(`/api/v1/classes/learn-office`, data)

/**
 * 学习官列表数据查询
 * @param {*} data
 * @returns
 */
export const getLearnOfficeListApi = data =>
  request.get(`/api/v1/user/lo/page?${stringifyParams(data)}`)

/**
 * 获取管理班级下班级列表数据
 * /api/v1/classes/unbound-manage
 */
export const getManageClassListApi = data =>
  request.get(`/api/v1/classes/unbound-manage?${stringifyParams(data)}`)

/**
 * 添加学生
 * @param {*} data
 * @returns
 */
export const addStudentApi = (data1, data2) =>
  request.post(`/api/v1/classes/stu?${stringifyParams(data1)}`, data2)

/**
 * 删除学生
 * @param {*} data
 * @returns
 */
export const deleteStudentApi = data => request.delete(`/api/v1/classes/stu`, { data })

/**
 * 修改学生
 * @param {*} data
 * @returns
 */
export const updateStudentApi = data => request.put(`/api/v1/classes/stu`, data)

// 部长用户管理...........................................................................
/**
 * 添加部长
 * @param {*} data
 * @returns
 */
export const addMinisterUserApi = data => request.post(`/api/v1/user/minister`, data)

/**
 * 删除部长
 * @param {*} id
 * @returns
 */
export const deleteMinisterUserApi = id => request.delete(`/api/v1/user/minister?id=${id}`)

/**
 * 更新部长
 * @param {*} data
 * @returns
 */
export const updateMinisterUserApi = data => request.put(`/api/v1/user/minister`, data)
/**
 * 获取部长详情
 * @param {*} data
 * @returns
 */
export const getMinisterDetailApi = id => request.get(`/api/v1/user/minister/${id}`)
/**
 * 获取部长列表
 * @param {*} data
 * @returns
 */
export const getMinisterUserListApi = data =>
  request.get(`/api/v1/user/minister/page?${stringifyParams(data)}`)
/**
 * 修改密码
 */
export const updateMinisterUserPwdApi = data => request.post(`/api/v1/user/minister/pwd`, data)

/**
 * 导入IM
 */
export const importMinisterImApi = data => request.post(`/api/v1/user/minister/im-import`, data)

// 学习官管理...........................................................................
/**
 * 添加学习官
 * @param {*} data
 * @returns
 */
export const addLoApi = data => request.post(`/api/v1/user/lo`, data)
/**
 * 删除学习官
 * @param {*} id
 * @returns
 */
export const deleteLoApi = id => request.delete(`/api/v1/user/lo?id=${id}`)
/**
 * 更新学习官
 * @param {*} data
 * @returns
 */
export const updateLoApi = data => request.put(`/api/v1/user/lo`, data)
/**
 * 修改密码
 * @param {*} data
 * @returns
 */
export const updateLoPwdApi = data => request.post(`/api/v1/user/lo/pwd`, data)
/**
 * 获取学习官详情
 * @param {*} id
 * @returns
 */
export const getLoDetailApi = id => request.get(`/api/v1/user/lo/${id}`)
/**
 * 获取学习官列表
 * @param {*} data
 * @returns
 */
export const getLoListApi = data => request.get(`/api/v1/user/lo/page?${stringifyParams(data)}`)

/**
 * 导入IM
 */
export const importLoImApi = data => request.post(`/api/v1/user/lo/im-import`, data)

// 工具包管理相关接口...........................................................................
/**
 * 获取工具包列表
 * @param {*} data
 * @returns
 */
export const getToolkitListApi = data => request.get(`/api/v1/tool?${stringifyParams(data)}`)

/**
 * 获取工具包详情
 * @param {*} id
 * @returns
 */
export const getToolkitDetailApi = id => request.get(`/api/v1/tool/${id}`)

/**
 * 添加工具包
 * @param {*} data
 * @returns
 */
export const addToolkitApi = data => request.post(`/api/v1/tool`, data)

/**
 * 更新工具包
 * @param {*} data
 * @returns
 */
export const editToolkitApi = data => request.put(`/api/v1/tool`, data)

/**
 * 删除工具包
 * @param {*} data
 * @returns
 */
export const deleteToolkitApi = data => request.delete(`/api/v1/tool`, { data })

/**
 * 获取解决方案工具年级枚举
 * @param {*} data
 * @returns
 */
export const getSolutionGradeListApi = () => request.get(`/api/v1/public/solution-grade`)

/**
 * 获取工具适用对象枚举
 * @param {*} data
 * @returns
 */
export const getToolObjectTypeListApi = () => request.get(`/api/v1/public/tool-object-type`)

/**
 * 获取工具适用班级类型枚举
 * @param {*} data
 * @returns
 */
export const getToolClassTypeListApi = () => request.get(`/api/v1/public/tool-class-type`)

/**
 * 获取oss配置
 * @returns
 */
export const getSysFileConfigApi = fileSuffixName =>
  request.get(`/api/v1/sys-file/oss?fileSuffixName=${fileSuffixName}`)

/**
 * 文件上传
 * @param {*} data
 * @returns
 */
export const uploadFileApi = data => request.put(`/api/v1/sys-file/file`, data)
/**
 * 删除文件
 * @param {*} id
 * @returns
 */
export const deleteFileApi = id => request.delete(`/api/v1/sys-file?fileId=${id}`)

// 解决方案管理...............................................................
/**
 * 获取解决方案列表
 * @param {*} data
 * @returns
 */
export const getSolutionListApi = data => request.get(`/api/v1/solution?${stringifyParams(data)}`)

/**
 * 获取解决方案详情
 * @param {*} id
 * @returns
 */
export const getSolutionDetailApi = id => request.get(`/api/v1/solution/${id}`)

/**
 * 获取解决方案相关工作列表
 * @param {*} data
 * @returns
 */
export const getPublicTaskTypeListApi = () => request.get(`/api/v1/public/task-type`)

/**
 * 获取问题类型列表
 * @param {*} data
 * @returns
 */
export const getQuestionTypeListApi = () => request.get(`/api/v1/public/question-type`)
/**
 * 添加解决方案
 * @param {*} data
 * @returns
 */
export const addSolutionApi = data => request.post(`/api/v1/solution`, data)

/**
 * 删除解决方案
 * @param {*} data
 * @returns
 */
export const deteleSolutionApi = data => request.delete(`/api/v1/solution`, { data })

/**
 * 编辑解决方案
 * @param {*} data
 * @returns
 */
export const editSolutionApi = data => request.put(`/api/v1/solution`, data)

// 轮播图管理相关接口...........................................................................
/**
 * 获取轮播图列表
 * @param {*} data
 * @returns
 */
export const getBannerListApi = data => request.get(`/api/v1/banner?${stringifyParams(data)}`)

/**
 * 添加轮播图
 * @param {*} data
 * @returns
 */
export const addBannerApi = data => request.post(`/api/v1/banner`, data)

/**
 * 删除轮播图
 * @param {*} id
 * @returns
 */
export const deleteBannerApi = data => request.delete(`/api/v1/banner`, { data })

/**
 * 更新轮播图
 * @param {*} data
 * @returns
 */
export const editBannerApi = data => request.put(`/api/v1/banner`, data)

// 德育主题管理...........................................................
/**
 * 获取德育主题列表
 * @param {*} data
 * @returns
 */
export const getThemeListApi = data => request.get(`/api/v1/theme?${stringifyParams(data)}`)

/**
 * 添加德育主题
 * @param {*} data
 * @returns
 */
export const addThemeApi = data => request.post(`/api/v1/theme`, data)

/**
 * 删除德育主题
 * @param {*} id
 * @returns
 */
export const deleteThemeApi = data => request.delete(`/api/v1/theme`, { data })

/**
 * 更新德育主题
 * @param {*} data
 * @returns
 */
export const editThemeApi = data => request.put(`/api/v1/theme`, data)

/**
 * 获取德育主题详情
 * @param {*} id
 * @returns
 */
export const getThemeDetailApi = id => request.get(`/api/v1/theme/${id}`)
