{"name": "oa-bms", "version": "1.0.0", "description": "劝学后台管理系统", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "preview:test": "vite preview --mode test", "preview:prod": "vite preview --mode production", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueup/vue-quill": "^1.2.0", "ant-design-vue": "^4.0.0", "axios": "^1.5.0", "dayjs": "^1.11.9", "lodash": "^4.17.21", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "qs": "^6.14.0", "quill": "^2.0.3", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.47.0", "eslint-plugin-vue": "^9.17.0", "prettier": "^3.0.2", "sass": "^1.66.1", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^4.4.9"}}