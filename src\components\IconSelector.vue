<template>
  <a-modal
    v-model:open="visible"
    title="选择图标"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="icon-selector">
      <!-- 搜索框 -->
      <div class="search-box">
        <a-input v-model:value="searchText" placeholder="搜索图标名称" allow-clear>
          <template #prefix>
            <SearchOutlined />
          </template>
        </a-input>
      </div>

      <!-- 图标分类标签 -->
      <div class="category-tabs">
        <a-radio-group v-model:value="activeCategory" button-style="solid">
          <a-radio-button value="all">全部</a-radio-button>
          <a-radio-button value="direction">方向性图标</a-radio-button>
          <a-radio-button value="suggestion">提示建议性图标</a-radio-button>
          <a-radio-button value="editor">编辑类图标</a-radio-button>
          <a-radio-button value="data">数据类图标</a-radio-button>
          <a-radio-button value="brand">品牌和标识</a-radio-button>
          <a-radio-button value="application">应用类图标</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 图标网格 -->
      <div class="icon-grid">
        <div
          v-for="icon in filteredIcons"
          :key="icon.name"
          class="icon-item"
          :class="{ active: selectedIcon === icon.name }"
          @click="selectIcon(icon.name)"
        >
          <component :is="icon.component" class="icon" />
          <div class="icon-name">{{ icon.name }}</div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredIcons.length === 0" class="empty-state">
        <a-empty description="未找到匹配的图标" />
      </div>

      <!-- 底部操作按钮 -->
      <div class="footer-actions">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :disabled="!selectedIcon" @click="handleConfirm">
            确定
          </a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup name="IconSelector">
import {
  // 搜索图标
  SearchOutlined,

  // 方向性图标
  DownOutlined,
  UpOutlined,
  LeftOutlined,
  RightOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  LeftCircleOutlined,
  RightCircleOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
  ForwardOutlined,
  BackwardOutlined,
  EnterOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  LoginOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,

  // 提示建议性图标
  QuestionOutlined,
  QuestionCircleOutlined,
  PlusOutlined,
  PlusCircleOutlined,
  PauseOutlined,
  PauseCircleOutlined,
  MinusOutlined,
  MinusCircleOutlined,
  InfoOutlined,
  InfoCircleOutlined,
  ExclamationOutlined,
  ExclamationCircleOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined,

  // 编辑类图标
  EditOutlined,
  FormOutlined,
  CopyOutlined,
  DeleteOutlined,
  SnippetsOutlined,
  HighlightOutlined,
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  RedoOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,

  // 数据类图标
  AreaChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  DotChartOutlined,
  LineChartOutlined,
  RadarChartOutlined,
  FallOutlined,
  RiseOutlined,
  StockOutlined,
  FundOutlined,
  SlidersOutlined,

  // 品牌和标识
  AndroidOutlined,
  AppleOutlined,
  WindowsOutlined,
  ChromeOutlined,
  GithubOutlined,
  WechatOutlined,
  AlipayOutlined,
  TaobaoOutlined,
  QqOutlined,
  DingdingOutlined,
  AntDesignOutlined,
  GoogleOutlined,
  FacebookOutlined,
  LinkedinOutlined,
  InstagramOutlined,
  SkypeOutlined,
  SlackOutlined,
  ZhihuOutlined,

  // 应用类图标
  HomeOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  DashboardOutlined,
  AppstoreOutlined,
  MenuOutlined,
  BarsOutlined,
  MailOutlined,
  BellOutlined,
  CalendarOutlined,
  FileOutlined,
  FolderOutlined,
  PictureOutlined,
  CameraOutlined,
  VideoCameraOutlined,
  MobileOutlined,
  TabletOutlined,
  LaptopOutlined,
  DesktopOutlined,
  PrinterOutlined,
  ScanOutlined,
  CloudOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  WifiOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  SafetyOutlined,
  BankOutlined,
  ShopOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  CreditCardOutlined,
  WalletOutlined,
  GiftOutlined,
  TrophyOutlined,
  CrownOutlined,
  HeartOutlined,
  StarOutlined,
  LikeOutlined,
  SmileOutlined,
  FireOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BookOutlined,
  ReadOutlined,
  BulbOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  BuildOutlined,
  BugOutlined,
  CodeOutlined,
  ApiOutlined,
  LinkOutlined,
  ShareAltOutlined,
  SendOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  SyncOutlined,
  ReloadOutlined,
  LoadingOutlined,
  PoweroffOutlined,
  HistoryOutlined,
  ScheduleOutlined,
  CarryOutOutlined,
  AuditOutlined,
  ProfileOutlined,
  ProjectOutlined,
  SolutionOutlined,
  TableOutlined,
  LayoutOutlined,
  FilterOutlined,
  TagOutlined,
  TagsOutlined,
  FlagOutlined,
  CompassOutlined,
  EnvironmentOutlined,
  AimOutlined,
  PushpinOutlined
} from "@ant-design/icons-vue"

// Props定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultIcon: {
    type: String,
    default: ""
  }
})

// Emits定义
const emit = defineEmits(["update:modelValue", "confirm"])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: value => emit("update:modelValue", value)
})

const searchText = ref("")
const activeCategory = ref("all")
const selectedIcon = ref(props.defaultIcon)

// 图标数据
const iconData = [
  // 方向性图标
  { name: "DownOutlined", component: DownOutlined, category: "direction" },
  { name: "UpOutlined", component: UpOutlined, category: "direction" },
  { name: "LeftOutlined", component: LeftOutlined, category: "direction" },
  { name: "RightOutlined", component: RightOutlined, category: "direction" },
  { name: "CaretUpOutlined", component: CaretUpOutlined, category: "direction" },
  { name: "CaretDownOutlined", component: CaretDownOutlined, category: "direction" },
  { name: "CaretLeftOutlined", component: CaretLeftOutlined, category: "direction" },
  { name: "CaretRightOutlined", component: CaretRightOutlined, category: "direction" },
  { name: "UpCircleOutlined", component: UpCircleOutlined, category: "direction" },
  { name: "DownCircleOutlined", component: DownCircleOutlined, category: "direction" },
  { name: "LeftCircleOutlined", component: LeftCircleOutlined, category: "direction" },
  { name: "RightCircleOutlined", component: RightCircleOutlined, category: "direction" },
  { name: "DoubleRightOutlined", component: DoubleRightOutlined, category: "direction" },
  { name: "DoubleLeftOutlined", component: DoubleLeftOutlined, category: "direction" },
  { name: "ForwardOutlined", component: ForwardOutlined, category: "direction" },
  { name: "BackwardOutlined", component: BackwardOutlined, category: "direction" },
  { name: "EnterOutlined", component: EnterOutlined, category: "direction" },
  { name: "ArrowUpOutlined", component: ArrowUpOutlined, category: "direction" },
  { name: "ArrowDownOutlined", component: ArrowDownOutlined, category: "direction" },
  { name: "ArrowLeftOutlined", component: ArrowLeftOutlined, category: "direction" },
  { name: "ArrowRightOutlined", component: ArrowRightOutlined, category: "direction" },
  { name: "LoginOutlined", component: LoginOutlined, category: "direction" },
  { name: "LogoutOutlined", component: LogoutOutlined, category: "direction" },
  { name: "MenuFoldOutlined", component: MenuFoldOutlined, category: "direction" },
  { name: "MenuUnfoldOutlined", component: MenuUnfoldOutlined, category: "direction" },
  { name: "FullscreenOutlined", component: FullscreenOutlined, category: "direction" },
  { name: "FullscreenExitOutlined", component: FullscreenExitOutlined, category: "direction" },

  // 提示建议性图标
  { name: "QuestionOutlined", component: QuestionOutlined, category: "suggestion" },
  { name: "QuestionCircleOutlined", component: QuestionCircleOutlined, category: "suggestion" },
  { name: "PlusOutlined", component: PlusOutlined, category: "suggestion" },
  { name: "PlusCircleOutlined", component: PlusCircleOutlined, category: "suggestion" },
  { name: "PauseOutlined", component: PauseOutlined, category: "suggestion" },
  { name: "PauseCircleOutlined", component: PauseCircleOutlined, category: "suggestion" },
  { name: "MinusOutlined", component: MinusOutlined, category: "suggestion" },
  { name: "MinusCircleOutlined", component: MinusCircleOutlined, category: "suggestion" },
  { name: "InfoOutlined", component: InfoOutlined, category: "suggestion" },
  { name: "InfoCircleOutlined", component: InfoCircleOutlined, category: "suggestion" },
  { name: "ExclamationOutlined", component: ExclamationOutlined, category: "suggestion" },
  {
    name: "ExclamationCircleOutlined",
    component: ExclamationCircleOutlined,
    category: "suggestion"
  },
  { name: "CloseOutlined", component: CloseOutlined, category: "suggestion" },
  { name: "CloseCircleOutlined", component: CloseCircleOutlined, category: "suggestion" },
  { name: "CheckOutlined", component: CheckOutlined, category: "suggestion" },
  { name: "CheckCircleOutlined", component: CheckCircleOutlined, category: "suggestion" },
  { name: "ClockCircleOutlined", component: ClockCircleOutlined, category: "suggestion" },
  { name: "WarningOutlined", component: WarningOutlined, category: "suggestion" },
  { name: "StopOutlined", component: StopOutlined, category: "suggestion" },

  // 编辑类图标
  { name: "EditOutlined", component: EditOutlined, category: "editor" },
  { name: "FormOutlined", component: FormOutlined, category: "editor" },
  { name: "CopyOutlined", component: CopyOutlined, category: "editor" },
  { name: "DeleteOutlined", component: DeleteOutlined, category: "editor" },
  { name: "SnippetsOutlined", component: SnippetsOutlined, category: "editor" },
  { name: "HighlightOutlined", component: HighlightOutlined, category: "editor" },
  { name: "AlignCenterOutlined", component: AlignCenterOutlined, category: "editor" },
  { name: "AlignLeftOutlined", component: AlignLeftOutlined, category: "editor" },
  { name: "AlignRightOutlined", component: AlignRightOutlined, category: "editor" },
  { name: "BoldOutlined", component: BoldOutlined, category: "editor" },
  { name: "ItalicOutlined", component: ItalicOutlined, category: "editor" },
  { name: "UnderlineOutlined", component: UnderlineOutlined, category: "editor" },
  { name: "RedoOutlined", component: RedoOutlined, category: "editor" },
  { name: "UndoOutlined", component: UndoOutlined, category: "editor" },
  { name: "ZoomInOutlined", component: ZoomInOutlined, category: "editor" },
  { name: "ZoomOutOutlined", component: ZoomOutOutlined, category: "editor" },
  { name: "FontColorsOutlined", component: FontColorsOutlined, category: "editor" },
  { name: "FontSizeOutlined", component: FontSizeOutlined, category: "editor" },
  { name: "SortAscendingOutlined", component: SortAscendingOutlined, category: "editor" },
  { name: "SortDescendingOutlined", component: SortDescendingOutlined, category: "editor" },
  { name: "OrderedListOutlined", component: OrderedListOutlined, category: "editor" },
  { name: "UnorderedListOutlined", component: UnorderedListOutlined, category: "editor" },

  // 数据类图标
  { name: "AreaChartOutlined", component: AreaChartOutlined, category: "data" },
  { name: "PieChartOutlined", component: PieChartOutlined, category: "data" },
  { name: "BarChartOutlined", component: BarChartOutlined, category: "data" },
  { name: "DotChartOutlined", component: DotChartOutlined, category: "data" },
  { name: "LineChartOutlined", component: LineChartOutlined, category: "data" },
  { name: "RadarChartOutlined", component: RadarChartOutlined, category: "data" },
  { name: "FallOutlined", component: FallOutlined, category: "data" },
  { name: "RiseOutlined", component: RiseOutlined, category: "data" },
  { name: "StockOutlined", component: StockOutlined, category: "data" },
  { name: "FundOutlined", component: FundOutlined, category: "data" },
  { name: "SlidersOutlined", component: SlidersOutlined, category: "data" },

  // 品牌和标识
  { name: "AndroidOutlined", component: AndroidOutlined, category: "brand" },
  { name: "AppleOutlined", component: AppleOutlined, category: "brand" },
  { name: "WindowsOutlined", component: WindowsOutlined, category: "brand" },
  { name: "ChromeOutlined", component: ChromeOutlined, category: "brand" },
  { name: "GithubOutlined", component: GithubOutlined, category: "brand" },
  { name: "WechatOutlined", component: WechatOutlined, category: "brand" },
  { name: "AlipayOutlined", component: AlipayOutlined, category: "brand" },
  { name: "TaobaoOutlined", component: TaobaoOutlined, category: "brand" },
  { name: "QqOutlined", component: QqOutlined, category: "brand" },
  { name: "DingdingOutlined", component: DingdingOutlined, category: "brand" },
  { name: "AntDesignOutlined", component: AntDesignOutlined, category: "brand" },
  { name: "GoogleOutlined", component: GoogleOutlined, category: "brand" },
  { name: "FacebookOutlined", component: FacebookOutlined, category: "brand" },
  { name: "LinkedinOutlined", component: LinkedinOutlined, category: "brand" },
  { name: "InstagramOutlined", component: InstagramOutlined, category: "brand" },
  { name: "SkypeOutlined", component: SkypeOutlined, category: "brand" },
  { name: "SlackOutlined", component: SlackOutlined, category: "brand" },
  { name: "ZhihuOutlined", component: ZhihuOutlined, category: "brand" },

  // 应用类图标
  { name: "HomeOutlined", component: HomeOutlined, category: "application" },
  { name: "SettingOutlined", component: SettingOutlined, category: "application" },
  { name: "UserOutlined", component: UserOutlined, category: "application" },
  { name: "TeamOutlined", component: TeamOutlined, category: "application" },
  { name: "DashboardOutlined", component: DashboardOutlined, category: "application" },
  { name: "AppstoreOutlined", component: AppstoreOutlined, category: "application" },
  { name: "MenuOutlined", component: MenuOutlined, category: "application" },
  { name: "BarsOutlined", component: BarsOutlined, category: "application" },
  { name: "SearchOutlined", component: SearchOutlined, category: "application" },
  { name: "MailOutlined", component: MailOutlined, category: "application" },
  { name: "BellOutlined", component: BellOutlined, category: "application" },
  { name: "CalendarOutlined", component: CalendarOutlined, category: "application" },
  { name: "FileOutlined", component: FileOutlined, category: "application" },
  { name: "FolderOutlined", component: FolderOutlined, category: "application" },
  { name: "PictureOutlined", component: PictureOutlined, category: "application" },
  { name: "CameraOutlined", component: CameraOutlined, category: "application" },
  { name: "VideoCameraOutlined", component: VideoCameraOutlined, category: "application" },
  { name: "MobileOutlined", component: MobileOutlined, category: "application" },
  { name: "TabletOutlined", component: TabletOutlined, category: "application" },
  { name: "LaptopOutlined", component: LaptopOutlined, category: "application" },
  { name: "DesktopOutlined", component: DesktopOutlined, category: "application" },
  { name: "PrinterOutlined", component: PrinterOutlined, category: "application" },
  { name: "ScanOutlined", component: ScanOutlined, category: "application" },
  { name: "CloudOutlined", component: CloudOutlined, category: "application" },
  { name: "DatabaseOutlined", component: DatabaseOutlined, category: "application" },
  { name: "GlobalOutlined", component: GlobalOutlined, category: "application" },
  { name: "WifiOutlined", component: WifiOutlined, category: "application" },
  { name: "LockOutlined", component: LockOutlined, category: "application" },
  { name: "UnlockOutlined", component: UnlockOutlined, category: "application" },
  { name: "KeyOutlined", component: KeyOutlined, category: "application" },
  { name: "SafetyOutlined", component: SafetyOutlined, category: "application" },
  { name: "BankOutlined", component: BankOutlined, category: "application" },
  { name: "ShopOutlined", component: ShopOutlined, category: "application" },
  { name: "ShoppingOutlined", component: ShoppingOutlined, category: "application" },
  { name: "ShoppingCartOutlined", component: ShoppingCartOutlined, category: "application" },
  { name: "CreditCardOutlined", component: CreditCardOutlined, category: "application" },
  { name: "WalletOutlined", component: WalletOutlined, category: "application" },
  { name: "GiftOutlined", component: GiftOutlined, category: "application" },
  { name: "TrophyOutlined", component: TrophyOutlined, category: "application" },
  { name: "CrownOutlined", component: CrownOutlined, category: "application" },
  { name: "HeartOutlined", component: HeartOutlined, category: "application" },
  { name: "StarOutlined", component: StarOutlined, category: "application" },
  { name: "LikeOutlined", component: LikeOutlined, category: "application" },
  { name: "SmileOutlined", component: SmileOutlined, category: "application" },
  { name: "FireOutlined", component: FireOutlined, category: "application" },
  { name: "EyeOutlined", component: EyeOutlined, category: "application" },
  { name: "EyeInvisibleOutlined", component: EyeInvisibleOutlined, category: "application" },
  { name: "BookOutlined", component: BookOutlined, category: "application" },
  { name: "ReadOutlined", component: ReadOutlined, category: "application" },
  { name: "BulbOutlined", component: BulbOutlined, category: "application" },
  { name: "RocketOutlined", component: RocketOutlined, category: "application" },
  { name: "ThunderboltOutlined", component: ThunderboltOutlined, category: "application" },
  { name: "ToolOutlined", component: ToolOutlined, category: "application" },
  { name: "BuildOutlined", component: BuildOutlined, category: "application" },
  { name: "BugOutlined", component: BugOutlined, category: "application" },
  { name: "CodeOutlined", component: CodeOutlined, category: "application" },
  { name: "ApiOutlined", component: ApiOutlined, category: "application" },
  { name: "LinkOutlined", component: LinkOutlined, category: "application" },
  { name: "ShareAltOutlined", component: ShareAltOutlined, category: "application" },
  { name: "SendOutlined", component: SendOutlined, category: "application" },
  { name: "UploadOutlined", component: UploadOutlined, category: "application" },
  { name: "DownloadOutlined", component: DownloadOutlined, category: "application" },
  { name: "ImportOutlined", component: ImportOutlined, category: "application" },
  { name: "ExportOutlined", component: ExportOutlined, category: "application" },
  { name: "SyncOutlined", component: SyncOutlined, category: "application" },
  { name: "ReloadOutlined", component: ReloadOutlined, category: "application" },
  { name: "LoadingOutlined", component: LoadingOutlined, category: "application" },
  { name: "PoweroffOutlined", component: PoweroffOutlined, category: "application" },
  { name: "HistoryOutlined", component: HistoryOutlined, category: "application" },
  { name: "ScheduleOutlined", component: ScheduleOutlined, category: "application" },
  { name: "CarryOutOutlined", component: CarryOutOutlined, category: "application" },
  { name: "AuditOutlined", component: AuditOutlined, category: "application" },
  { name: "ProfileOutlined", component: ProfileOutlined, category: "application" },
  { name: "ProjectOutlined", component: ProjectOutlined, category: "application" },
  { name: "SolutionOutlined", component: SolutionOutlined, category: "application" },
  { name: "TableOutlined", component: TableOutlined, category: "application" },
  { name: "LayoutOutlined", component: LayoutOutlined, category: "application" },
  { name: "FilterOutlined", component: FilterOutlined, category: "application" },
  { name: "TagOutlined", component: TagOutlined, category: "application" },
  { name: "TagsOutlined", component: TagsOutlined, category: "application" },
  { name: "FlagOutlined", component: FlagOutlined, category: "application" },
  { name: "CompassOutlined", component: CompassOutlined, category: "application" },
  { name: "EnvironmentOutlined", component: EnvironmentOutlined, category: "application" },
  { name: "AimOutlined", component: AimOutlined, category: "application" },
  { name: "PushpinOutlined", component: PushpinOutlined, category: "application" }
]

// 计算属性 - 过滤图标
const filteredIcons = computed(() => {
  let icons = iconData

  // 按分类过滤
  if (activeCategory.value !== "all") {
    icons = icons.filter(icon => icon.category === activeCategory.value)
  }

  // 按搜索文本过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase()
    icons = icons.filter(icon => icon.name.toLowerCase().includes(searchLower))
  }

  return icons
})

// 方法
const selectIcon = iconName => {
  selectedIcon.value = iconName
}

const handleConfirm = () => {
  emit("confirm", selectedIcon.value)
  visible.value = false
}

const handleCancel = () => {
  selectedIcon.value = props.defaultIcon
  visible.value = false
}

// 监听默认图标变化
watch(
  () => props.defaultIcon,
  newVal => {
    selectedIcon.value = newVal
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.icon-selector {
  .search-box {
    margin-bottom: 16px;
  }

  .category-tabs {
    margin-bottom: 20px;

    :deep(.ant-radio-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    :deep(.ant-radio-button-wrapper) {
      border-radius: 6px;
      border: 1px solid #d9d9d9;

      &:not(:first-child) {
        margin-left: 0;
      }

      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }

  .icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background-color: #fafafa;

    .icon-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 12px 8px;
      border: 1px solid transparent;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s ease;
      background-color: #fff;

      &:hover {
        border-color: #40a9ff;
        background-color: #f6ffed;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.active {
        border-color: #1890ff;
        background-color: #e6f7ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      .icon {
        font-size: 24px;
        color: #666;
        margin-bottom: 8px;
        transition: color 0.2s ease;
      }

      .icon-name {
        font-size: 12px;
        color: #999;
        text-align: center;
        word-break: break-all;
        line-height: 1.2;
      }

      &:hover .icon,
      &.active .icon {
        color: #1890ff;
      }

      &:hover .icon-name,
      &.active .icon-name {
        color: #1890ff;
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    text-align: center;
  }

  .footer-actions {
    margin-top: 20px;
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
  }
}

// 滚动条样式
.icon-grid::-webkit-scrollbar {
  width: 6px;
}

.icon-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.icon-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
.ant-radio-button-wrapper::before {
  display: none;
}
</style>
