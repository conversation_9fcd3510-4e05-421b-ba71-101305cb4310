# 图标选择器组件 (IconSelector)

一个基于 Ant Design Vue 的图标选择器弹窗组件，支持搜索、分类筛选和单选功能。

## 功能特性

- 🎯 **丰富的图标库**：包含 Ant Design Vue 的所有常用图标
- 🔍 **智能搜索**：支持按图标名称搜索
- 📂 **分类筛选**：按功能分类展示图标（方向性、提示建议性、编辑类、数据类、品牌标识、应用类）
- ✨ **美观的UI**：现代化的网格布局，支持悬停效果和选中状态
- 📱 **响应式设计**：自适应不同屏幕尺寸
- 🎨 **自定义样式**：优雅的滚动条和过渡动画

## 使用方法

### 基础用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button @click="showIconSelector">选择图标</a-button>
    
    <!-- 图标选择器 -->
    <IconSelector
      v-model="iconSelectorVisible"
      :default-icon="selectedIcon"
      @confirm="handleIconConfirm"
    />
    
    <!-- 显示选中的图标 -->
    <component v-if="selectedIcon" :is="getIconComponent(selectedIcon)" />
  </div>
</template>

<script setup>
import IconSelector from '@/components/IconSelector.vue'
import * as AntIcons from '@ant-design/icons-vue'

const iconSelectorVisible = ref(false)
const selectedIcon = ref('HomeOutlined')

const showIconSelector = () => {
  iconSelectorVisible.value = true
}

const handleIconConfirm = (iconName) => {
  selectedIcon.value = iconName
  console.log('选择的图标:', iconName)
}

const getIconComponent = (iconName) => {
  return AntIcons[iconName] || null
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 控制弹窗显示/隐藏 | `boolean` | `false` |
| defaultIcon | 默认选中的图标名称 | `string` | `''` |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 弹窗显示状态改变时触发 | `(visible: boolean)` |
| confirm | 确认选择图标时触发 | `(iconName: string)` |

## 图标分类

### 方向性图标 (direction)
包含箭头、导航、方向指示等图标，如：`ArrowUpOutlined`、`LeftOutlined`、`MenuFoldOutlined` 等

### 提示建议性图标 (suggestion)  
包含提示、警告、状态指示等图标，如：`CheckOutlined`、`CloseOutlined`、`WarningOutlined` 等

### 编辑类图标 (editor)
包含编辑、格式化、操作等图标，如：`EditOutlined`、`CopyOutlined`、`DeleteOutlined` 等

### 数据类图标 (data)
包含图表、统计、数据展示等图标，如：`BarChartOutlined`、`PieChartOutlined`、`LineChartOutlined` 等

### 品牌和标识 (brand)
包含各种品牌logo和社交媒体图标，如：`GithubOutlined`、`WechatOutlined`、`AlipayOutlined` 等

### 应用类图标 (application)
包含常用的应用功能图标，如：`HomeOutlined`、`SettingOutlined`、`UserOutlined` 等

## 样式定制

组件使用 SCSS 编写样式，支持以下 CSS 变量定制：

```scss
.icon-selector {
  // 自定义主题色
  --primary-color: #1890ff;
  --hover-color: #40a9ff;
  --border-color: #d9d9d9;
  --background-color: #fafafa;
}
```

## 注意事项

1. 确保项目中已安装 `@ant-design/icons-vue`
2. 组件依赖 Vue 3 的 Composition API
3. 需要在项目中正确配置 Ant Design Vue
4. 图标名称必须是有效的 Ant Design Vue 图标名称

## 示例

查看 `IconSelectorDemo.vue` 文件获取完整的使用示例。 