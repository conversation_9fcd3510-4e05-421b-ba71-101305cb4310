<!-- 解决方案管理 -->
<template>
  <div class="solution-management">
    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="适用对象" class="marginBot">
          <a-select
            v-model:value="searchForm.ProblemObj"
            placeholder="请选择对象"
            style="width: 150px"
            allow-clear
          >
            <a-select-option v-for="item in objectTypes" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="适用年级" class="marginBot">
          <a-select
            v-model:value="searchForm.SolutionSemesterEnums"
            placeholder="请选择年级"
            style="width: 330px"
            allow-clear
            :max-tag-count="2"
            mode="multiple"
            :options="grades"
          >
          </a-select>
        </a-form-item>

        <a-form-item label="适用班级类型" class="marginBot">
          <a-select
            v-model:value="searchForm.ToolClassType"
            placeholder="请选择班级类型"
            style="width: 150px"
            allow-clear
          >
            <a-select-option v-for="item in classTypes" :key="item.value" :value="item.value">
              {{ item.text }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="问题描述" class="marginBot">
          <a-input
            v-model:value="searchForm.ProblemTitle"
            placeholder="请输入问题描述"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>

        <a-form-item class="marginBot">
          <a-button type="primary" html-type="submit" :loading="loading"> 搜索 </a-button>
          <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮区域 -->
    <a-card class="operation-card" :bordered="false">
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd"> 创建解决方案 </a-button>
        <a-button @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0" danger>
          删除
        </a-button>
        <span class="selected-info" v-if="selectedRowKeys.length > 0">
          已选择 {{ selectedRowKeys.length }} 项
        </span>
      </div>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :row-selection="rowSelection"
        :pagination="pagination"
        :loading="loading"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'problemObj'">
            {{ getObjectTypeName(record.problemObj) }}
          </template>

          <template v-if="column.key === 'problemSemesters'">
            <div class="grade-tags-container">
              <!-- 始终显示的前3个标签 -->
              <a-tag
                v-for="(item, index) in record.problemSemesters.slice(0, 3)"
                :key="item.id"
                color="green"
                style="margin-bottom: 4px; margin-right: 4px"
              >
                {{ item.solutionSemesterName }}
              </a-tag>

              <!-- 当超过3个时的处理 -->
              <template v-if="record.problemSemesters.length > 3">
                <!-- 展开状态：显示剩余的所有标签 -->
                <template v-if="expandedRows[record.id]">
                  <a-tag
                    v-for="(item, index) in record.problemSemesters.slice(3)"
                    :key="item.id"
                    color="green"
                    style="margin-bottom: 4px; margin-right: 4px"
                  >
                    {{ item.solutionSemesterName }}
                  </a-tag>
                  <a-button
                    type="link"
                    size="small"
                    @click="toggleExpand(record.id)"
                    style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                  >
                    收起
                  </a-button>
                </template>

                <!-- 收起状态：显示"展开"按钮和剩余数量 -->
                <template v-else>
                  <a-button
                    type="link"
                    size="small"
                    @click="toggleExpand(record.id)"
                    style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                  >
                    +{{ record.problemSemesters.length - 3 }}个
                  </a-button>
                </template>
              </template>
            </div>
          </template>

          <template v-if="column.key === 'toolClassType'">
            <a-tag :color="'blue'">
              {{ getClassTypeName(record.toolClassType) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'upTime'">
            {{ formatDate(record.upTime) }}
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleDetail(record)"> 详情 </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建解决方案弹窗 -->
    <a-modal
      v-model:open="openCreate"
      :destroyOnClose="true"
      :maskClosable="false"
      :width="currentStep === 4 ? '1200px' : '800px'"
      :title="'创建解决方案'"
      :footer="null"
      @cancel="close"
    >
      <!-- 步骤条区域 -->
      <!-- <a-button @click="log">测试按钮</a-button> -->
      <div
        style="
          display: flex;
          gap: 20px;
          justify-content: center;
          margin-bottom: 40px;
          padding: 10px;
          background-color: aliceblue;
        "
      >
        <div v-for="item in steps" :key="item.id">
          <div style="display: flex; gap: 10px; align-items: center">
            <div
              class="step-item"
              style="display: flex; gap: 5px; align-items: center; cursor: pointer"
              @click="handleStepClick(item.id)"
            >
              <div
                class="step-number"
                :style="{
                  width: '30px',
                  height: '30px',
                  textAlign: 'center',
                  lineHeight: '30px',
                  borderRadius: '30px',
                  border: '1px solid #ccc',
                  color: currentStep === item.id ? '#fff' : '#000',
                  backgroundColor: currentStep === item.id ? '#1890ff' : '#fff'
                }"
              >
                {{ item.id }}
              </div>
              <div
                class="step-title"
                :style="{ color: currentStep === item.id ? '#1890ff' : '#000' }"
              >
                {{ item.title }}
              </div>
            </div>
            <span v-if="item.id !== 4"><RightOutlined /></span>
          </div>
        </div>
      </div>
      <!-- 内容区 -->
      <div
        style="min-height: 300px; padding: 0 100px"
        :style="{ padding: currentStep === 4 ? '0 20px' : '0 100px' }"
      >
        <!-- 解决方案适用范围 -->
        <div v-if="currentStep === 1">
          <a-form layout="vertical" :model="creatFormData">
            <a-form-item label="解决方案适用对象" required>
              <a-select
                v-model:value="creatFormData.problemObj"
                placeholder="请选择对象"
                style="width: 100%"
                allow-clear
              >
                <a-select-option v-for="item in objectTypes" :key="item.value" :value="item.value">
                  {{ item.text }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="工具适用年级" name="problemSemesters" required>
              <div
                class="grade-select-wrapper"
                style="display: flex; align-items: center; gap: 10px"
              >
                <a-select
                  v-model:value="creatFormData.problemSemesters"
                  mode="multiple"
                  placeholder="请选择工具适用年级"
                  style="width: 100%"
                  :max-tag-count="3"
                  allow-clear
                  show-search
                  :filter-option="filterOption"
                  :options="grades"
                  @change="handleEditGradeSelectChange"
                >
                </a-select>
                <div class="grade-select-header" style="width: 180px">
                  <a-checkbox
                    v-model:checked="selectAllGrades"
                    :indeterminate="indeterminate"
                    @change="handleSelectAllGrades"
                  >
                    全选
                  </a-checkbox>
                  <span class="selected-count" style="color: #8d8d8d; font-size: 12px">
                    已选择 {{ creatFormData.problemSemesters.length }} 项
                  </span>
                </div>
              </div>
            </a-form-item>
            <a-form-item label="适用班级类型" class="marginBot" required>
              <div
                class="grade-select-wrapper"
                style="display: flex; align-items: center; gap: 10px"
              >
                <a-select
                  v-model:value="creatFormData.toolClassType"
                  placeholder="请选择班级类型"
                  style="width: 100%"
                  allow-clear
                >
                  <a-select-option v-for="item in classTypes" :key="item.value" :value="item.value">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </div>
            </a-form-item>
          </a-form>
        </div>
        <!-- 问题详情 -->
        <div v-if="currentStep === 2">
          <a-form layout="vertical" :model="creatFormData">
            <a-form-item label="问题描述" name="problemTitle" required>
              <a-input v-model:value="creatFormData.problemTitle" placeholder="请输入问题描述" />
            </a-form-item>
            <a-form-item label="显著现象" name="problemPhenomenon" required>
              <div
                v-for="(item, index) in creatFormData.problemPhenomenon"
                :key="index"
                style="margin-bottom: 8px"
              >
                <div style="display: flex; gap: 8px; align-items: center">
                  <a-input
                    v-model:value="creatFormData.problemPhenomenon[index]"
                    :placeholder="`请输入问题对应的显著现象描述${index + 1}`"
                    style="flex: 1"
                  />
                  <a-button
                    type="text"
                    danger
                    size="small"
                    @click="removePhenomenon(index)"
                    :disabled="creatFormData.problemPhenomenon.length === 1"
                  >
                    删除
                  </a-button>
                </div>
              </div>
              <a-button type="dashed" style="width: 100%; margin-top: 30px" @click="addPhenomenon">
                + 添加显著现象描述
              </a-button>
            </a-form-item>
          </a-form>
        </div>
        <!-- 解决方案详情 -->
        <div v-if="currentStep === 3">
          <a-form layout="vertical" :model="creatFormData">
            <a-form-item label="解决方案文本描述" name="solutionContent" required>
              <QuillEditor
                v-model:content="creatFormData.solutionContent"
                contentType="html"
                placeholder="请输入解决方案的详细描述..."
                :options="editorOptions"
                class="rich-text-editor"
              />
            </a-form-item>
            <a-form-item label="解决方案相关工作创建" name="problemTaskType" required>
              <a-select
                mode="multiple"
                show-search
                v-model:value="creatFormData.problemTaskType"
                placeholder="请选择解决方案相关工作"
                :filter-option="filterOption"
                :options="taskTypes"
              >
                <!-- <a-select-option v-for="item in taskTypes" :key="item.value" :value="item.value">
                  {{ item.text }}
                </a-select-option> -->
              </a-select>
            </a-form-item>
          </a-form>
        </div>

        <!-- 解决方案配套工具 -->
        <div v-if="currentStep === 4">
          <!-- 搜索区域 -->
          <a-card class="search-card" :bordered="false" bodyStyle="padding: 0">
            <a-form layout="inline" :model="searchFormModal" @finish="handleSearchModal">
              <a-form-item label="工具名称" class="marginBot">
                <a-input
                  v-model:value="searchFormModal.ToolName"
                  placeholder="请输入工具名称"
                  style="width: 140px"
                  allow-clear
                />
              </a-form-item>

              <a-form-item label="适用对象" class="marginBot">
                <a-select
                  v-model:value="searchFormModal.ToolObjectType"
                  placeholder="请选择对象"
                  style="width: 130px"
                  allow-clear
                >
                  <a-select-option
                    v-for="item in objectTypes"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item label="适用年级" class="marginBot">
                <a-select
                  v-model:value="searchFormModal.SolutionSemesterEnums"
                  placeholder="请选择年级"
                  style="width: 150px"
                  allow-clear
                  show-search
                  :max-tag-count="1"
                  mode="multiple"
                  :filter-option="filterOption"
                  :options="grades"
                >
                </a-select>
              </a-form-item>

              <a-form-item label="适用班级类型" class="marginBot">
                <a-select
                  v-model:value="searchFormModal.ToolClassType"
                  placeholder="请选择适用班级类型"
                  style="width: 170px"
                  allow-clear
                >
                  <a-select-option v-for="item in classTypes" :key="item.value" :value="item.value">
                    {{ item.text }}
                  </a-select-option>
                </a-select>
              </a-form-item>

              <a-form-item class="marginBot">
                <a-button type="primary" html-type="submit" :loading="loading"> 搜索 </a-button>
                <a-button style="margin-left: 8px" @click="handleResetModal(false)">
                  重置
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 操作按钮区域 -->
          <!-- <a-card class="operation-card" :bordered="false">
            <div class="table-operations">
              <span class="selected-info" v-if="selectedRowKeys.length > 0">
                已选择 {{ selectedRowKeys.length }} 项
              </span>
            </div>
          </a-card> -->

          <!-- 表格区域 -->
          <a-card :bordered="false" bodyStyle="padding: 10px 0;margin-bottom: 10px">
            <a-card-grid style="width: 70%; text-align: center" :hoverable="false">
              <a-table
                :columns="columnsModal"
                :data-source="dataSourceModal"
                :row-selection="rowSelectionModal"
                :pagination="paginationModal"
                :loading="loadingModal"
                row-key="id"
                size="small"
                style="height: 100%"
                :scroll="{ y: 300 }"
                @change="handleTableChangeModal"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'toolSize'">
                    {{ formatFileSize(record.toolSize) }}
                  </template>

                  <template v-if="column.key === 'problemObj'">
                    {{ getObjectTypeName(record.problemObj) }}
                  </template>

                  <template v-if="column.key === 'toolKitSemesters'">
                    <div class="grade-tags-container">
                      <!-- 始终显示的前3个标签 -->
                      <a-tag
                        v-for="(item, index) in record.toolKitSemesters.slice(0, 3)"
                        :key="item.id"
                        color="green"
                        style="margin-bottom: 4px; margin-right: 4px"
                      >
                        {{ item.solutionSemesterName }}
                      </a-tag>

                      <!-- 当超过3个时的处理 -->
                      <template v-if="record.toolKitSemesters.length > 3">
                        <!-- 展开状态：显示剩余的所有标签 -->
                        <template v-if="modalExpandedRows[record.id]">
                          <a-tag
                            v-for="(item, index) in record.toolKitSemesters.slice(3)"
                            :key="item.id"
                            color="green"
                            style="margin-bottom: 4px; margin-right: 4px"
                          >
                            {{ item.solutionSemesterName }}
                          </a-tag>
                          <a-button
                            type="link"
                            size="small"
                            @click="toggleModalExpand(record.id)"
                            style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                          >
                            收起
                          </a-button>
                        </template>

                        <!-- 收起状态：显示"展开"按钮和剩余数量 -->
                        <template v-else>
                          <a-button
                            type="link"
                            size="small"
                            @click="toggleModalExpand(record.id)"
                            style="padding: 0; height: auto; line-height: 1; color: #1890ff"
                          >
                            +{{ record.toolKitSemesters.length - 3 }}个
                          </a-button>
                        </template>
                      </template>
                    </div>
                  </template>

                  <template v-if="column.key === 'toolClassType'">
                    {{ getClassTypeName(record.toolClassType) }}
                  </template>

                  <template v-if="column.key === 'upTime'">
                    {{ formatDate(record.upTime) }}
                  </template>
                </template>
              </a-table>
            </a-card-grid>
            <a-card-grid style="width: 30%" :hoverable="false">
              <h3 style="font-size: 12px; color: #1677ff">已选择的配套工具</h3>
              <!-- {{ selectedRowItemsModal }} -->
              <div
                v-if="selectedRowItemsModal.length > 0"
                style="
                  height: 350px;
                  overflow-y: auto;
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  align-items: flex-start;
                "
              >
                <a-tag
                  color="green"
                  v-for="item in selectedRowItemsModal"
                  :key="item.id"
                  closable
                  @close="removeTag(item.id)"
                >
                  {{ item.toolName }}
                </a-tag>
              </div>
              <a-empty v-else style="padding-top: 100px" />
            </a-card-grid>
          </a-card>
        </div>
      </div>

      <!-- 底部按钮区 -->
      <div style="display: flex; justify-content: center; gap: 100px">
        <a-button v-if="currentStep === 1" @click="close">取消</a-button>
        <a-button v-if="[2, 3, 4].includes(currentStep)" @click="handlePrev">上一步</a-button>
        <a-button type="primary" v-if="[1, 2, 3].includes(currentStep)" @click="handleNext"
          >下一步</a-button
        >
        <a-button type="primary" v-if="currentStep === 4" @click="submit" :loading="loadingSubmit"
          >完成创建</a-button
        >
      </div>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="openDetail"
      :bodyStyle="{ maxHeight: '500px', overflowY: 'auto' }"
      width="800px"
      title="解决方案详情"
      @ok="handleOk"
    >
      <a-descriptions title="" :column="1" bordered size="small" class="detail-descriptions">
        <a-descriptions-item label="问题描述">
          {{ detailData.problemTitle }}
        </a-descriptions-item>
        <a-descriptions-item label="解决方案适用对象">
          <a-tag color="blue">
            {{ getObjectTypeName(detailData.problemObj) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="解决方案适用年级">
          <a-tag
            style="margin-bottom: 6px"
            color="green"
            v-for="item in detailData.problemSemesters"
            :key="item.solutionSemesterName"
          >
            {{ item.solutionSemesterName }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="工具适用班级类型">
          <a-tag color="cyan">
            {{ getClassTypeName(detailData.toolClassType) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="显著现象">
          <ul style="padding-left: 10px" v-if="detailData.problemPhenomenon">
            <li v-for="(item, index) in parsePhenomenon(detailData.problemPhenomenon)" :key="index">
              {{ item }}
            </li>
          </ul>
          <span v-else>-</span>
        </a-descriptions-item>
        <a-descriptions-item label="解决方案文本描述">
          <div v-html="detailData.solutionContent"></div>
        </a-descriptions-item>

        <a-descriptions-item label="解决方案相关工作创建">
          <a-tag
            color="#f50"
            style="margin-bottom: 6px"
            v-for="item in detailData.problemTaskTypes"
            :key="item.taskTypeEnumName"
          >
            {{ item.taskTypeEnumName }}
          </a-tag>
        </a-descriptions-item>

        <a-descriptions-item label="相关配套工具">
          <a-tag color="#009733" v-for="item in detailData.toolKits" :key="item.id">
            {{ item.toolName }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup name="Solution">
import { ref, reactive, onMounted, computed } from "vue"
import { message } from "ant-design-vue"
import { ExclamationCircleOutlined, RightOutlined } from "@ant-design/icons-vue"
import { createVNode } from "vue"
import { Modal } from "ant-design-vue"
import dayjs from "dayjs"
import {
  getSolutionListApi,
  getToolObjectTypeListApi,
  getSolutionGradeListApi,
  getToolClassTypeListApi,
  getSolutionDetailApi,
  getToolkitListApi,
  getPublicTaskTypeListApi,
  addSolutionApi,
  deteleSolutionApi,
  editSolutionApi
} from "@/api"
import { QuillEditor } from "@vueup/vue-quill"
import "@vueup/vue-quill/dist/vue-quill.snow.css"
import qs from "qs"

// 响应式数据
const loading = ref(false)
const dataSource = ref([])
const selectedRowKeys = ref([])

// 搜索表单
const searchForm = reactive({
  ProblemObj: undefined,
  SolutionSemesterEnums: undefined,
  ToolClassType: undefined,
  ProblemTitle: ""
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条数据，显示第 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ["10", "20", "50", "100"]
})

// 表格列配置
const columns = [
  {
    title: "问题描述",
    dataIndex: "problemTitle",
    key: "problemTitle",
    width: 200,
    ellipsis: true
  },
  {
    title: "修改时间",
    dataIndex: "upTime",
    key: "upTime",
    width: 150
  },
  {
    title: "适用对象",
    dataIndex: "problemObj",
    key: "problemObj",
    width: 100
  },
  {
    title: "适用年级",
    dataIndex: "problemSemesters",
    key: "problemSemesters",
    width: 200
  },
  {
    title: "适用班级类型",
    dataIndex: "toolClassType",
    key: "toolClassType",
    width: 150
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right"
  }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: keys => {
    selectedRowKeys.value = keys
  }
}

// 基础数据
const objectTypes = ref([])

const grades = ref([])

const classTypes = ref([])

/**
 * 获取解决方案列表数据
 */
const getSolutionList = async () => {
  try {
    loading.value = true
    const params = {
      PageSize: pagination.pageSize,
      PageIndex: pagination.current,
      ...searchForm
    }
    // 过滤掉空值
    Object.keys(params).forEach(key => {
      if (params[key] === "" || params[key] === undefined || params[key] === null) {
        delete params[key]
      }
    })
    if (params.SolutionSemesterEnums === 0) {
      delete params.SolutionSemesterEnums
    }
    console.log("params", qs.stringify(params, { arrayFormat: "repeat" }))
    getSolutionListApi(params).then(res => {
      if (res.code === 200) {
        // res.data.items[0].problemSemesters = [
        //   ...res.data.items[0].problemSemesters,
        //   ...res.data.items[0].problemSemesters
        // ]
        dataSource.value = res.data.items
        pagination.total = res.data.total
      }
    })
  } catch (error) {
    console.error("获取解决方案列表失败:", error)
  } finally {
    loading.value = false
  }
}

/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  getSolutionList()
}

/**
 * 重置搜索
 */
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    if (typeof searchForm[key] === "string") {
      searchForm[key] = ""
    } else {
      searchForm[key] = undefined
    }
  })
  pagination.current = 1
  getSolutionList()
}

/**
 * 表格变化处理
 */
const handleTableChange = pag => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  getSolutionList()
}

/**
 * 添加解决方案
 */
const handleAdd = () => {
  console.log("创建解决方案")
  openCreate.value = true
  currentStep.value = 1
}

/**
 * 批量删除
 */
const handleBatchDelete = () => {
  Modal.confirm({
    title: "确定删除这些解决方案吗？",
    icon: createVNode(ExclamationCircleOutlined),
    onOk() {
      deteleSolutionApi(selectedRowKeys.value).then(res => {
        if (res.code === 200) {
          message.success("删除成功")
          getSolutionList()
        }
      })
    }
  })
}

/**
 * 查看详情
 */
const openDetail = ref(false)
const detailData = ref({})
const handleDetail = record => {
  openDetail.value = true
  getSolutionDetailApi(record.id).then(res => {
    if (res.code === 200) {
      console.log("查看详情", res)
      detailData.value = res.data
    }
  })
}
const handleOk = () => {
  openDetail.value = false
  // 重置详情弹窗中年级标签的展开状态
  detailExpanded.value = false
}

/**
 * 编辑
 */
const handleEdit = async record => {
  openCreate.value = true
  getSolutionDetailApi(record.id).then(res => {
    if (res.code === 200) {
      console.log("编辑时获取详情", res)
      creatFormData.value = {
        problemObj: res.data.problemObj,
        problemSemesters: (res.data.problemSemesters || []).map(i => i.solutionSemesterEnum),
        toolClassType: res.data.toolClassType,
        problemTitle: res.data.problemTitle,
        problemPhenomenon: JSON.parse(res.data.problemPhenomenon),
        solutionContent: res.data.solutionContent,
        // 选择的解决方案配套工具id
        problemTools: (res.data.toolKits || []).map(i => i.id),
        problemTaskType: (res.data.problemTaskTypes || []).map(i => i.taskTypeEnum),
        id: res.data.id
      }
      selectedRowKeysModal.value = creatFormData.value.problemTools
      selectedRowItemsModal.value = res.data.toolKits || []

      // 初始化全局映射
      selectedItemsMap.value.clear()
      ;(res.data.toolKits || []).forEach(item => {
        selectedItemsMap.value.set(item.id, item)
      })

      console.log("编辑时初始化映射:", Array.from(selectedItemsMap.value.entries()))
    }
  })
}

/**
 * 格式化日期
 */
const formatDate = date => {
  if (!date) return "-"
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss")
}

/**
 * 获取对象类型名称
 */
const getObjectTypeName = value => {
  const item = objectTypes.value.find(item => item.value === value)
  return item ? item.text : "-"
}
/**
 * 获取班级类型名称
 */
const getClassTypeName = value => {
  const item = classTypes.value.find(item => item.value === value)
  return item ? item.text : "-"
}

/**
 * 创建解决方案弹窗相关
 */
/**
 * 过滤年级选项
 */
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const currentStep = ref(1)
const openCreate = ref(false)
const steps = ref([
  {
    id: 1,
    title: "解决方案适用范围"
  },
  {
    id: 2,
    title: "问题详情"
  },
  {
    id: 3,
    title: "解决方案详情"
  },
  {
    id: 4,
    title: "解决方案配套工具"
  }
])
const creatFormData = ref({
  // 解决方案适用范围
  problemObj: undefined,
  problemSemesters: [],
  toolClassType: undefined,
  // 问题详情
  problemTitle: undefined,
  problemPhenomenon: [""], // 默认包含一个空的显著现象项
  // 解决方案详情
  solutionContent: undefined,
  problemTaskType: [],
  // 解决方案配套工具
  problemTools: []
})
// const log = () => {
//   console.log(creatFormData.value)
// }
// 全选功能相关变量
const selectAllGrades = ref(false) // 全选状态
const indeterminate = ref(false) // 半选状态（部分选中）

// 响应式数据：控制年级标签的展开/收起状态
const expandedRows = ref({})

/**
 * 切换年级标签的展开/收起状态
 */
const toggleExpand = recordId => {
  expandedRows.value[recordId] = !expandedRows.value[recordId]
}

// 响应式数据：控制详情弹窗中适用年级标签的展开/收起状态
const detailExpanded = ref(false)

// 下一步
const handleNext = () => {
  currentStep.value++

  // 当切换到第四步时，同步第一步的数据到搜索条件
  if (currentStep.value === 4) {
    syncStep1DataToStep4()
    handleSearchModal()
  }
}
// 上一步
const handlePrev = () => {
  currentStep.value--
}
const handleStepClick = id => {
  currentStep.value = id
  console.log(id)

  // 当切换到第四步时，同步第一步的数据到搜索条件
  if (id === 4) {
    syncStep1DataToStep4()
    handleSearchModal()
  }
}

/**
 * 同步第一步的数据到第四步的搜索条件
 */
const syncStep1DataToStep4 = () => {
  console.log("开始同步第一步数据到第四步:", creatFormData.value)

  // 同步适用对象
  if (creatFormData.value.problemObj !== undefined) {
    searchFormModal.ToolObjectType = creatFormData.value.problemObj
    console.log("同步适用对象:", creatFormData.value.problemObj)
  }

  // 同步适用年级
  if (creatFormData.value.problemSemesters && creatFormData.value.problemSemesters.length > 0) {
    searchFormModal.SolutionSemesterEnums = [...creatFormData.value.problemSemesters]
    console.log("同步适用年级:", creatFormData.value.problemSemesters)
  }

  // 同步适用班级类型
  if (creatFormData.value.toolClassType !== undefined) {
    searchFormModal.ToolClassType = creatFormData.value.toolClassType
    console.log("同步适用班级类型:", creatFormData.value.toolClassType)
  }

  console.log("同步后的搜索条件:", searchFormModal)
}
// 弹窗表格搜索
// 搜索表单
/**
 * 获取表格列表数据
 */
const loadingModal = ref(false)
// 表格列配置
const columnsModal = [
  {
    title: "工具名称",
    dataIndex: "toolName",
    key: "toolName"
    // width: 150
  },
  // {
  //   title: "修改时间",
  //   dataIndex: "upTime",
  //   key: "upTime",
  //   width: 150
  // },
  // {
  //   title: "大小",
  //   dataIndex: "toolSize",
  //   key: "toolSize",
  //   width: 100
  // },
  {
    title: "适用对象",
    dataIndex: "problemObj",
    key: "problemObj"
    // width: 120
  },
  {
    title: "适用年级",
    dataIndex: "toolKitSemesters",
    key: "toolKitSemesters"
    // width: 120
  },
  {
    title: "适用班级类型",
    dataIndex: "toolClassType",
    key: "toolClassType"
    // width: 130
  }
  // {
  //   title: "操作",
  //   key: "action",
  //   width: 120,
  //   fixed: "right"
  // }
]
/**
 * 格式化文件大小
 */
const formatFileSize = size => {
  if (size === null || size === undefined || size === 0) return "-"

  // 输入的size是字节单位，需要转换为合适的单位显示
  if (size < 1024) {
    return size + " B"
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + " KB"
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + " MB"
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(1) + " GB"
  }
}
// 分页配置
const paginationModal = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条数据，显示第 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ["10", "20", "50", "100"]
})

// 弹窗表格选中的行id数组
const selectedRowKeysModal = ref([])
// 弹窗表格选中的行数组
const selectedRowItemsModal = ref([])
// 维护一个全局的已选择项目映射，key为id，value为完整的项目数据
const selectedItemsMap = ref(new Map())

// 计算当前页面的选中行ID（只包含当前页面存在的行）
const currentPageSelectedKeys = computed(() => {
  const currentPageIds = dataSourceModal.value.map(item => item.id)
  const allSelectedIds = Array.from(selectedItemsMap.value.keys())
  return allSelectedIds.filter(id => currentPageIds.includes(id))
})

const rowSelectionModal = {
  selectedRowKeys: currentPageSelectedKeys,
  onChange: (keys, items) => {
    console.log("选择变化 - keys:", keys, "当前页items:", items)

    // 更新全局已选择项目映射
    updateSelectedItemsMap(keys, items)

    // 重建已选择项目数组
    rebuildSelectedItemsArray()

    // 更新表单数据（使用全局的选中ID）
    selectedRowKeysModal.value = Array.from(selectedItemsMap.value.keys())
    creatFormData.value.problemTools = selectedRowKeysModal.value
  }
}

/**
 * 更新全局已选择项目映射
 */
const updateSelectedItemsMap = (newKeys, currentPageItems) => {
  // 首先，从映射中移除当前页面所有项目（无论是否被选中）
  const currentPageIds = dataSourceModal.value.map(item => item.id)
  currentPageIds.forEach(id => {
    selectedItemsMap.value.delete(id)
  })

  // 然后，将当前页面新选中的项目添加到映射中
  currentPageItems.forEach(item => {
    if (newKeys.includes(item.id)) {
      selectedItemsMap.value.set(item.id, item)
    }
  })

  console.log("更新后的映射:", Array.from(selectedItemsMap.value.entries()))
}

/**
 * 根据映射重建已选择项目数组
 */
const rebuildSelectedItemsArray = () => {
  selectedRowItemsModal.value = Array.from(selectedItemsMap.value.values())
  console.log(
    "重建后的已选择项目:",
    selectedRowItemsModal.value.map(item => ({ id: item.id, name: item.toolName }))
  )
}
const dataSourceModal = ref([])
const taskTypes = ref([])
const getToolkitList = async () => {
  try {
    // debugger
    loadingModal.value = true

    const params = {
      PageSize: paginationModal.pageSize,
      PageIndex: paginationModal.current,
      ...searchFormModal
    }
    // 过滤掉空值
    Object.keys(params).forEach(key => {
      if (params[key] === "" || params[key] === undefined || params[key] === null) {
        delete params[key]
      }
    })
    if (params.SolutionSemesterEnums === 0) {
      delete params.SolutionSemesterEnums
    }
    const res = await getToolkitListApi(params)

    if (res && res.code === 200) {
      dataSourceModal.value = res.data?.items || []
      paginationModal.total = res.data?.total || 0

      // 数据加载完成后，同步表格选中状态
      syncTableSelectionState()
    } else {
      message.error(res?.msg || "获取数据失败")
    }
  } catch (error) {
    console.error("获取工具包列表失败:", error)
    // message.error("获取数据失败")
  } finally {
    loadingModal.value = false
  }
}

/**
 * 同步表格选中状态
 * 确保表格显示的选中状态与全局已选择的数据一致
 */
const syncTableSelectionState = () => {
  console.log("表格数据加载完成，当前选中状态:", {
    当前页面IDs: dataSourceModal.value.map(item => item.id),
    全部已选择IDs: Array.from(selectedItemsMap.value.keys()),
    当前页面已选择IDs: currentPageSelectedKeys.value
  })
  // 更新全局选中ID数组（用于表单提交）
  selectedRowKeysModal.value = Array.from(selectedItemsMap.value.keys())
  creatFormData.value.problemTools = selectedRowKeysModal.value
}
const searchFormModal = reactive({
  ToolObjectType: undefined,
  SolutionSemesterEnums: undefined,
  ToolClassType: undefined,
  ToolName: ""
})
const handleSearchModal = () => {
  paginationModal.current = 1
  getToolkitList()
}
const removeTag = id => {
  console.log("删除标签:", id)

  // 从选中的key数组中移除
  selectedRowKeysModal.value = selectedRowKeysModal.value.filter(i => i !== id)

  // 从全局映射中移除
  selectedItemsMap.value.delete(id)

  // 重建已选择项目数组
  rebuildSelectedItemsArray()

  // 更新表单数据
  creatFormData.value.problemTools = selectedRowKeysModal.value

  console.log("删除后状态:", {
    keys: selectedRowKeysModal.value,
    mapSize: selectedItemsMap.value.size
  })
}
const handleResetModal = (bool = false) => {
  Object.keys(searchFormModal).forEach(key => {
    if (typeof searchFormModal[key] === "string") {
      searchFormModal[key] = ""
    } else {
      searchFormModal[key] = undefined
    }
  })
  paginationModal.current = 1
  // 如果不是从close函数调用，则重新同步第一步数据并搜索
  if (!bool) {
    // syncStep1DataToStep4()
    getToolkitList()
  }
}

/**
 * 弹窗表格分页处理
 */
const handleTableChangeModal = pag => {
  paginationModal.current = pag.current
  paginationModal.pageSize = pag.pageSize
  getToolkitList()
}
const close = () => {
  openCreate.value = false
  // 重置表单数据
  creatFormData.value = {
    // 解决方案适用范围
    problemObj: undefined,
    problemSemesters: [],
    toolClassType: undefined,
    // 问题详情
    problemTitle: undefined,
    problemPhenomenon: [""], // 重置为默认一个空项
    // 解决方案详情
    solutionContent: undefined,
    problemTaskType: [],
    // 解决方案配套工具
    problemTools: []
  }
  // 重置全选状态
  selectAllGrades.value = false
  indeterminate.value = false
  // 重置步骤
  currentStep.value = 1
  // 重置弹窗表格选中状态
  selectedRowKeysModal.value = []
  // 重置弹窗表格中年级标签的展开状态
  modalExpandedRows.value = {}
  //  重置创建解决方案中表格查询条件
  handleResetModal(true)
  // 重置创建解决方案中已选择的配套工具数组
  selectedRowItemsModal.value = []
  // 清理全局已选择项目映射
  selectedItemsMap.value.clear()
}
/**
 * 校验表单数据
 */
const validateFormData = () => {
  const data = creatFormData.value
  const errors = []

  // 第一步：解决方案适用范围
  if (!data.problemObj) {
    errors.push({ step: 1, message: "请选择解决方案适用对象" })
  }

  if (!data.problemSemesters || data.problemSemesters.length === 0) {
    errors.push({ step: 1, message: "请选择工具适用年级" })
  }

  if (!data.toolClassType) {
    errors.push({ step: 1, message: "请选择适用班级类型" })
  }

  // 第二步：问题详情
  if (!data.problemTitle || data.problemTitle.trim() === "") {
    errors.push({ step: 2, message: "请输入问题描述" })
  }

  if (!data.problemPhenomenon || data.problemPhenomenon.length === 0) {
    errors.push({ step: 2, message: "请添加显著现象描述" })
  } else {
    // 检查显著现象是否有空的项目
    const hasEmptyPhenomenon = data.problemPhenomenon.some(item => !item || item.trim() === "")
    if (hasEmptyPhenomenon) {
      errors.push({ step: 2, message: "显著现象描述不能为空，请填写完整或删除空项" })
    }
  }

  // 第三步：解决方案详情
  if (!data.solutionContent || data.solutionContent.trim() === "") {
    errors.push({ step: 3, message: "请输入解决方案文本描述" })
  }

  if (!data.problemTaskType || data.problemTaskType.length === 0) {
    errors.push({ step: 3, message: "请选择解决方案相关工作创建" })
  }

  // 第四步：解决方案配套工具
  if (!data.problemTools || data.problemTools.length === 0) {
    errors.push({ step: 4, message: "请选择至少一个解决方案配套工具" })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * 提交数据
 */
const loadingSubmit = ref(false)
const submit = async () => {
  // 数据校验
  const validation = validateFormData()

  if (!validation.isValid) {
    const firstError = validation.errors[0]

    // 自动跳转到有问题的步骤
    currentStep.value = firstError.step

    // 如果是第四步，需要加载工具数据
    if (firstError.step === 4) {
      syncStep1DataToStep4()
      handleSearchModal()
    }

    // 显示错误信息
    message.error(firstError.message)
    console.log("表单校验失败:", validation.errors)
    return
  }

  loadingSubmit.value = true

  const reqParams = {
    ...creatFormData.value,
    problemPhenomenon: JSON.stringify(creatFormData.value.problemPhenomenon)
  }

  console.log("提交数据", reqParams)

  try {
    const isEdit = !!reqParams.id
    const apiCall = isEdit ? editSolutionApi(reqParams) : addSolutionApi(reqParams)
    const successMessage = isEdit ? "编辑成功" : "添加成功"

    const res = await apiCall

    if (res.code === 200) {
      message.success(successMessage)
      close()
      getSolutionList()
    } else {
      message.error(res.msg || `${isEdit ? "编辑" : "添加"}失败`)
    }
  } catch (error) {
    console.error("提交失败:", error)
  } finally {
    loadingSubmit.value = false
  }
}

/**
 * 处理年级全选
 */
const handleSelectAllGrades = checked => {
  const checkedStatus = checked.target.checked
  if (checkedStatus) {
    // 全选：选中所有年级
    creatFormData.value.problemSemesters = [...grades.value.map(item => item.value)]
  } else {
    // 取消全选：清空选择
    creatFormData.value.problemSemesters = []
  }
  indeterminate.value = false
  selectAllGrades.value = checkedStatus
}

/**
 * 处理年级选择变化
 */
const handleEditGradeSelectChange = selectedValues => {
  creatFormData.value.problemSemesters = selectedValues
  // 更新全选状态
  selectAllGrades.value = selectedValues.length === grades.value.length && selectedValues.length > 0
  // 更新半选状态
  indeterminate.value = selectedValues.length > 0 && selectedValues.length < grades.value.length
}

/**
 * 添加显著现象描述
 */
const addPhenomenon = () => {
  creatFormData.value.problemPhenomenon.push("")
}

/**
 * 删除显著现象描述
 */
const removePhenomenon = index => {
  creatFormData.value.problemPhenomenon.splice(index, 1)
}

// 富文本编辑器配置
const editorOptions = ref({
  modules: {
    toolbar: [
      ["bold", "italic", "underline"], // 加粗，斜体，下划线
      [{ header: [1, 2, 3, false] }], // 标题
      [{ list: "ordered" }, { list: "bullet" }], // 有序列表，无序列表
      [{ indent: "-1" }, { indent: "+1" }], // 减少缩进，增加缩进
      [{ size: ["small", false, "large"] }], // 字体大小
      [{ color: [] }], // 字体颜色
      [{ align: [] }] // 对齐方式
      // ["clean"] // 清除文本格式
    ]
  },
  placeholder: "请输入解决方案的详细描述...",
  theme: "snow"
})

// 响应式数据：控制弹窗表格中年级标签的展开/收起状态
const modalExpandedRows = ref({})

/**
 * 切换弹窗表格中年级标签的展开/收起状态
 */
const toggleModalExpand = recordId => {
  modalExpandedRows.value[recordId] = !modalExpandedRows.value[recordId]
}

/**
 * 解析JSON字符串，处理undefined和空值
 */
const parsePhenomenon = jsonString => {
  if (!jsonString) {
    return []
  }
  try {
    const parsed = JSON.parse(jsonString)
    return Array.isArray(parsed) ? parsed : [parsed]
  } catch (e) {
    console.error("解析显著现象JSON失败:", e)
    return []
  }
}

// 初始化数据
onMounted(() => {
  // 获取列表数据
  getSolutionList()
  // 获取对象类型数据
  getToolObjectTypeListApi().then(res => {
    if (res.code === 200) {
      console.log("工具适用对象res", res)
      objectTypes.value = res.data || []
    }
  })
  // 获取年级数据
  getSolutionGradeListApi().then(res => {
    if (res.code === 200) {
      console.log("工具适用年级res", res)
      grades.value = (res.data || []).map(item => ({
        label: item.text,
        value: item.value
      }))
    }
  })
  // 获取工具适用班级类型
  getToolClassTypeListApi().then(res => {
    if (res.code === 200) {
      console.log("工具适用班级类型res", res)
      classTypes.value = res.data || []
    }
  })
  // 获取解决方案相关工作列表
  getPublicTaskTypeListApi().then(res => {
    if (res.code === 200) {
      console.log("解决方案相关工作列表res", res)
      taskTypes.value = (res.data || []).map(item => ({
        label: item.text,
        value: item.value
      }))
    }
  })
})
</script>

<style lang="scss" scoped>
.solution-management {
  .search-card {
    margin-bottom: 16px;
    .marginBot {
      margin-bottom: 10px;
    }
  }

  .operation-card {
    margin-bottom: 16px;

    .table-operations {
      display: flex;
      align-items: center;
      gap: 8px;

      .selected-info {
        margin-left: 16px;
        color: #666;
        font-size: 14px;
      }
    }
  }

  // 年级标签容器样式
  .grade-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
}
</style>

<!-- 全局样式，用于Modal弹窗 -->
<style lang="scss">
// 详情弹窗中descriptions的label宽度
.detail-descriptions .ant-descriptions-item-label {
  width: 200px !important;
}
.step-item:hover {
  .step-title {
    color: #1890ff !important;
  }
  .step-number {
    background-color: #1890ff !important;
    color: #fff !important;
  }
}
.search-card {
  .marginBot {
    margin-bottom: 10px;
  }
}
// 富文本编辑器样式
.rich-text-editor {
  .ql-editor {
    min-height: 150px;
    max-height: 300px;
    overflow-y: auto;
  }

  .ql-toolbar {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: none;
  }

  .ql-container {
    border-bottom: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-top: none;
  }
}
</style>
