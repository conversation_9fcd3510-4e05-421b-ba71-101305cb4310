<template>
  <div class="fund-management">
    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd">新增</a-button>
    </div>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'start_time'">
          {{ formatDate(record.start_time) }}
        </template>
        <template v-if="column.key === 'end_time'">
          {{ formatDate(record.end_time) }}
        </template>
        <template v-if="column.key === 'money'"> {{ formatAmount(record.money) }} </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="开始日期" name="start_time">
          <a-date-picker
            v-model:value="formData.start_time"
            placeholder="请选择开始日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </a-form-item>

        <a-form-item label="结束日期" name="end_time">
          <a-date-picker
            v-model:value="formData.end_time"
            placeholder="请选择结束日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </a-form-item>

        <a-form-item label="经费金额" name="money">
          <a-input-number
            v-model:value="formData.money"
            placeholder="请输入经费金额"
            :min="0"
            :max="100000000000000"
            :precision="2"
            style="width: 100%"
            :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
            :parser="value => value.replace(/¥\s?|(,*)/g, '')"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="Fund">
import dayjs from "dayjs"
import {
  addFinancialApi,
  getFinancialListApi,
  editFinancialApi,
  deleteFinancialApi,
  getSingleFinancialApi
} from "@/api/index.js"
import { max } from "lodash"

// 响应式数据
const modalVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const loading = ref(false)
const submitLoading = ref(false)
const currentRecord = ref(null)

// 表单数据
const formData = reactive({
  id: null,
  start_time: null,
  end_time: null,
  money: null
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    getFundList()
  },
  onShowSizeChange: (current, size) => {
    pagination.current = 1
    pagination.pageSize = size
    getFundList()
  }
})

// 表格列配置
const columns = [
  {
    title: "id",
    dataIndex: "id",
    key: "id"
  },
  {
    title: "开始日期",
    dataIndex: "start_time",
    key: "start_time"
  },
  {
    title: "结束日期",
    dataIndex: "end_time",
    key: "end_time"
  },
  {
    title: "经费金额",
    dataIndex: "money",
    key: "money"
  },
  {
    title: "操作",
    key: "action",
    width: 150
  }
]

// 表格数据（假数据）
const dataSource = ref([])

// 表单验证规则
const rules = {
  start_time: [{ required: true, message: "请选择开始日期", trigger: "change" }],
  end_time: [
    { required: true, message: "请选择结束日期", trigger: "change" },
    {
      validator: (rule, value) => {
        if (value && formData.start_time && dayjs(value).isBefore(dayjs(formData.start_time))) {
          return Promise.reject(new Error("结束日期不能早于开始日期"))
        }
        return Promise.resolve()
      },
      trigger: "change"
    }
  ],
  money: [
    { required: true, message: "请输入经费金额", trigger: "blur" },
    { type: "number", min: 0.01, message: "经费金额必须大于0", trigger: "blur" }
  ]
}

/**
 * 获取经费列表数据
 */
const getFundList = async () => {
  if (loading.value) {
    return
  }

  try {
    loading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize
    }

    const res = await getFinancialListApi(params)
    console.log("经费列表数据:", res)

    if (res.code === 200) {
      dataSource.value = res.data.items || []
      pagination.total = res.data.total || 0
    } else {
      message.error(res.msg || "获取数据失败")
    }
  } catch (error) {
    console.error("获取经费列表失败:", error)
  } finally {
    loading.value = false
  }
}

// 格式化日期
const formatDate = date => {
  if (!date) return "-"
  return dayjs(date).format("YYYY-MM-DD")
}

// 格式化金额
const formatAmount = money => {
  if (money === null || money === undefined) return "0.00"
  return Number(money).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 新增
const handleAdd = () => {
  modalTitle.value = "新增"
  modalVisible.value = true
  currentRecord.value = null
  Object.assign(formData, {
    id: null,
    start_time: null,
    end_time: null,
    money: null
  })
}

// 编辑
const handleEdit = async record => {
  modalTitle.value = "编辑"
  modalVisible.value = true
  currentRecord.value = record

  try {
    // 调用查询单个经费信息接口
    const res = await getSingleFinancialApi(record.id)
    console.log("获取单个经费信息:", res)

    if (res.code === 200) {
      const data = res.data
      Object.assign(formData, {
        id: data.id,
        start_time: data.start_time ? dayjs(data.start_time).format("YYYY-MM-DD") : null,
        end_time: data.end_time ? dayjs(data.end_time).format("YYYY-MM-DD") : null,
        money: data.money
      })
    } else {
      // 如果获取详情失败，使用传入的record数据
      Object.assign(formData, {
        id: record.id,
        start_time: record.start_time ? dayjs(record.start_time).format("YYYY-MM-DD") : null,
        end_time: record.end_time ? dayjs(record.end_time).format("YYYY-MM-DD") : null,
        money: record.money
      })
      message.warning("获取详情失败，使用当前数据")
    }
  } catch (error) {
    // 使用传入的record数据作为备选
    Object.assign(formData, {
      id: record.id,
      start_time: record.start_time ? dayjs(record.start_time).format("YYYY-MM-DD") : null,
      end_time: record.end_time ? dayjs(record.end_time).format("YYYY-MM-DD") : null,
      money: record.money
    })
  }
}

// 删除
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除这条经费记录吗？`,
    async onOk() {
      try {
        const res = await deleteFinancialApi([record.id])
        if (res.code === 200) {
          message.success("删除成功")
          getFundList()
        } else {
          message.error(res.msg || "删除失败")
        }
      } catch (error) {
        console.error("删除失败:", error)
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 处理字段映射
    const params = {
      start_time: formData.start_time
        ? dayjs(formData.start_time).format("YYYY-MM-DDTHH:mm:ss")
        : null,
      end_time: formData.end_time ? dayjs(formData.end_time).format("YYYY-MM-DDTHH:mm:ss") : null,
      money: formData.money
    }

    if (formData.id) {
      // 编辑 - 调用编辑接口
      params.id = formData.id
      const res = await editFinancialApi(params)
      if (res.code === 200) {
        message.success("更新成功")
        modalVisible.value = false
        getFundList() // 刷新列表
      } else {
        message.error(res.msg || "更新失败")
      }
    } else {
      // 新增 - 调用新增接口
      const res = await addFinancialApi(params)
      if (res.code === 200) {
        message.success("新增成功")
        modalVisible.value = false
        getFundList() // 刷新列表
      } else {
        message.error(res.msg || "新增失败")
      }
    }
  } catch (error) {
    console.error("提交失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  submitLoading.value = false
}

// 组件挂载时获取数据
onMounted(() => {
  getFundList()
})
</script>

<style lang="scss" scoped>
.fund-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}
</style>
