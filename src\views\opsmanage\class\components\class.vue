<!-- 班级管理 -->
<template>
  <div class="class-management-cp">
    <!-- 学习官 -->
    <div class="lo ct">
      <div class="title">学习官</div>
      <div>
        <a-tag :color="loData?.realName ? '#87d068' : '#cccccc'">{{
          loData?.realName ? loData?.realName : "请选择学习官"
        }}</a-tag>
        <a-button type="link" size="small" @click="showLearnOfficeModal">选择</a-button>
      </div>
    </div>
    <!-- 教师管理 -->
    <div class="tm ct">
      <div class="title">教师管理</div>
      <div>
        <span v-if="tcData?.length > 0">
          <a-tag color="#87d068" v-for="i in tcData" :key="i.id">
            {{ i.subName }}老师：{{ i.teacherName }}
          </a-tag>
        </span>
        <a-tag v-else color="#cccccc">{{ "请添加教师" }}</a-tag>
        <!-- <a-button type="link" size="small" @click="addTeacher">添加教师</a-button> -->
        <!-- deleteTeacherApi -->
        <!-- <a-button type="link" size="small" @click="deleteTeacher">删除教师</a-button> -->
      </div>
    </div>
    <!-- 学生管理 -->
    <div class="sm ct">
      <div class="title">学生管理</div>
      <!-- 添加学生按钮 -->
      <div class="student-operations">
        <a-button type="primary" size="small" @click="showAddStudentModal">添加学生</a-button>
        <a-button
          style="margin-left: 10px"
          type="primary"
          size="small"
          @click="showImportStudentModal"
          >导入学生</a-button
        >
      </div>

      <!-- 学生表格 -->
      <a-table
        :columns="studentColumns"
        :data-source="studentData"
        :pagination="studentPagination"
        row-key="id"
        size="middle"
        class="student-table"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="editStudent(record)">修改</a-button>
              <a-popconfirm
                title="确定删除吗?"
                ok-text="是"
                cancel-text="否"
                @confirm="deleteStudent(record)"
              >
                <a href="#" style="color: #ff4d4f">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 学习官选择弹窗 -->
    <a-modal v-model:open="learnOfficeModalVisible" title="选择学习官" width="800px" :footer="null">
      <!-- 查询区域 -->
      <div class="search-area" style="margin-bottom: 20px">
        <a-form layout="inline" :model="searchForm">
          <a-form-item label="姓名">
            <a-input
              v-model:value="searchForm.Name"
              placeholder="请输入姓名"
              style="width: 150px"
            />
          </a-form-item>
          <a-form-item label="账号">
            <a-input
              v-model:value="searchForm.Account"
              placeholder="请输入账号"
              style="width: 150px"
            />
          </a-form-item>
          <a-form-item>
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="handleReset">重置</a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </div>

      <!-- 表格区域 -->
      <a-table
        :columns="learnOfficeColumns"
        :data-source="learnOfficeData"
        :pagination="false"
        row-key="id"
        size="middle"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'isTeamLeader'">
            <a-tag :color="record.isTeamLeader ? 'green' : 'default'">
              {{ record.isTeamLeader ? "是" : "否" }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-button type="primary" size="small" @click="selectLearnOffice(record)">
              选择
            </a-button>
          </template>
        </template>
      </a-table>
    </a-modal>

    <!-- 添加教师表单弹窗 -->
    <a-modal
      v-model:open="teacherModalVisible"
      title="添加教师"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleTeacherSubmit"
      @cancel="handleTeacherCancel"
    >
      <a-form
        ref="teacherFormRef"
        :model="teacherFormData"
        :rules="teacherFormRules"
        layout="vertical"
      >
        <a-form-item label="科目" name="subId">
          <a-select
            v-model:value="teacherFormData.subId"
            placeholder="请选择科目"
            style="width: 100%"
            show-search
            :filter-option="filterSubjectOption"
          >
            <a-select-option
              v-for="subject in subjectOptions"
              :key="subject.value"
              :value="subject.value"
              :label="subject.label"
            >
              {{ subject.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="教师姓名" name="teacherName">
          <a-input
            v-model:value="teacherFormData.teacherName"
            placeholder="请输入教师姓名"
            maxlength="20"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加学生弹窗 -->
    <a-modal
      v-model:open="addStudentModalVisible"
      title="添加学生"
      width="650px"
      :confirm-loading="addStudentLoading"
      @ok="saveStudents"
      @cancel="cancelAddStudent"
      ok-text="保存"
      cancel-text="取消"
      :bodyStyle="{ padding: '24px' }"
    >
      <div class="add-student-content">
        <!-- 动态输入行 -->
        <div class="student-input-container">
          <div v-for="(item, index) in studentInputList" :key="item.key" class="student-input-row">
            <div class="input-label">
              <span class="student-number">{{ index + 1 }}.</span>
              <span class="label-text">学生姓名</span>
            </div>
            <div class="input-control" style="display: flex; margin-bottom: 10px">
              <a-input
                v-model:value="item.name"
                placeholder="请输入学生姓名"
                size="large"
                :maxlength="20"
                show-count
              />
              <a-button
                type="text"
                danger
                size="large"
                @click="removeStudentInput(index)"
                :disabled="studentInputList.length === 1"
                class="delete-btn"
              >
                <template #icon>
                  <DeleteOutlined />
                </template>
              </a-button>
            </div>
          </div>
        </div>

        <!-- 添加一行按钮 -->
        <div class="add-row-section" style="margin-top: 30px">
          <a-button
            type="dashed"
            style="width: 100%; font-size: 14px"
            size="large"
            @click="addStudentInput"
            class="add-row-btn"
          >
            <template #icon>
              <PlusOutlined />
            </template>
            添加一行
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 编辑学生弹窗 -->
    <a-modal
      v-model:open="editStudentModalVisible"
      title="编辑学生"
      width="400px"
      :confirm-loading="editStudentLoading"
      @ok="saveEditStudent"
      @cancel="cancelEditStudent"
    >
      <a-form layout="vertical">
        <a-form-item label="学生姓名">
          <a-input
            v-model:value="editStudentForm.realName"
            placeholder="请输入学生姓名"
            maxlength="20"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 导入学生弹窗 -->
    <a-modal
      v-model:open="importStudentModalVisible"
      title="导入学生"
      width="800px"
      :confirm-loading="importStudentLoading"
      @ok="confirmImportStudents"
      @cancel="cancelImportStudents"
      ok-text="确认导入"
      cancel-text="取消"
    >
      <div class="import-student-content">
        <!-- <div class="import-student-tip">
          <a-alert
            message="使用说明"
            description="支持从Excel复制学生姓名数据粘贴到下方文本框中，系统会自动解析并生成学生列表"
            type="info"
            show-icon
          />
        </div> -->
        <a-alert
          message="支持从Excel复制学生姓名数据粘贴到下方文本框中，系统会自动解析并生成学生列表"
          type="info"
          show-icon
        />
        <div class="import-student-text" style="margin-top: 10px">
          <a-textarea
            v-model:value="pasteText"
            placeholder="请粘贴学生姓名数据，每行一个学生姓名"
            :rows="4"
            @change="handlePasteText"
          />
        </div>

        <div style="margin: 5px 0" class="import-student-actions" v-if="pasteText.trim()">
          <a-button type="primary" size="small" @click="handlePasteText"> 解析数据 </a-button>
          <a-button size="small" @click="clearPasteText" style="margin-left: 8px"> 清空 </a-button>
        </div>

        <div class="import-student-list" v-if="importStudentList.length > 0">
          <div class="list-header">
            <div class="header-left">
              <span>学生列表 (共 {{ importStudentList.length }} 人)</span>
            </div>
          </div>
          <a-table
            :columns="importStudentColumns"
            :data-source="importStudentList"
            row-key="key"
            size="small"
            :pagination="false"
            :scroll="{ y: 400 }"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'name'">
                <a-input
                  v-model:value="record.name"
                  placeholder="请输入学生姓名"
                  size="small"
                  @blur="updateImportStudentName(record, record.name)"
                  :maxlength="20"
                />
              </template>
              <template v-if="column.key === 'action'">
                <a-button type="link" size="small" danger @click="removeImportStudent(record)">
                  移除
                </a-button>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup name="ClassManageCp">
import { ref, reactive, computed, watch } from "vue"
import { message } from "ant-design-vue"
import { DeleteOutlined, PlusOutlined, SaveOutlined } from "@ant-design/icons-vue"
import {
  getLearnOfficeApi,
  addOrUpdateLearnOfficeApi,
  getLearnOfficeListApi,
  getTeacherListApi,
  // 新增老师查询科目数据
  getSubjectsListApi,
  //   新增教师
  addTeacherApi,
  //删除老师
  deleteTeacherApi,
  addStudentApi,
  getStudentsListApi,
  deleteStudentApi,
  updateStudentApi
} from "@/api/index.js"

const props = defineProps({
  classesId: {
    type: String,
    default: ""
  }
})

// 定义emit事件
const emit = defineEmits(["updateTree"])

// 弹窗控制
const learnOfficeModalVisible = ref(false)
const teacherModalVisible = ref(false)
const addStudentModalVisible = ref(false)
const editStudentModalVisible = ref(false)
const importStudentModalVisible = ref(false)
const submitLoading = ref(false)
const addStudentLoading = ref(false)
const editStudentLoading = ref(false)
const importStudentLoading = ref(false)

// 查询表单
const searchForm = reactive({
  Name: "",
  Account: ""
})

// 教师表单数据
const teacherFormData = reactive({
  subId: null,
  teacherName: ""
})

// 教师表单引用
const teacherFormRef = ref()

// 教师表单验证规则
const teacherFormRules = {
  subId: [{ required: true, message: "请选择科目", trigger: "change" }],
  teacherName: [
    { required: true, message: "请输入教师姓名", trigger: "blur" },
    { min: 2, max: 20, message: "教师姓名长度为2-20个字符", trigger: "blur" }
  ]
}

// 科目假数据
const subjectOptions = ref([
  { value: 1, label: "语文" },
  { value: 2, label: "数学" },
  { value: 3, label: "英语" },
  { value: 4, label: "物理" },
  { value: 5, label: "化学" },
  { value: 6, label: "生物" },
  { value: 7, label: "政治" },
  { value: 8, label: "历史" },
  { value: 9, label: "地理" },
  { value: 10, label: "体育" },
  { value: 11, label: "音乐" },
  { value: 12, label: "美术" },
  { value: 13, label: "信息技术" }
])

// 学习官数据
const learnOfficeData = ref([])

// 表格列配置
const learnOfficeColumns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    width: 60
  },
  {
    title: "姓名",
    dataIndex: "realName",
    key: "realName",
    width: 100
  },
  {
    title: "账号",
    dataIndex: "account",
    key: "account",
    width: 120
  },
  {
    title: "电话号码",
    dataIndex: "phone",
    key: "phone",
    width: 120
  },
  {
    title: "是否组长",
    dataIndex: "isTeamLeader",
    key: "isTeamLeader",
    width: 80
  },
  {
    title: "管理班级数",
    dataIndex: "followClassCount",
    key: "followClassCount",
    width: 100
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    fixed: "right"
  }
]

// 学生相关数据
const studentData = ref([])
// 添加分页数据
const studentPagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条数据，显示第 ${range[0]}-${range[1]} 条`,
  pageSizeOptions: ["10", "20", "50", "100"],
  size: "default"
})

// 学生表格列配置
const studentColumns = [
  {
    title: "姓名",
    dataIndex: "realName",
    key: "realName",
    width: 200
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right"
  }
]

// 添加学生输入列表
const studentInputList = ref([{ key: Date.now(), name: "" }])

// 编辑学生表单
const editStudentForm = reactive({
  id: null,
  realName: ""
})
const currentEditStudent = ref(null)

// 导入学生相关数据
const pasteText = ref("") // 粘贴的文本内容
const importStudentList = ref([]) // 导入的学生列表

// 导入学生表格列配置
const importStudentColumns = [
  {
    title: "序号",
    key: "index",
    width: 60,
    customRender: ({ index }) => index + 1
  },
  {
    title: "姓名",
    dataIndex: "name",
    key: "name",
    width: 200
  },
  {
    title: "操作",
    key: "action",
    width: 80,
    fixed: "right"
  }
]

// 查询学生列表
const getStudentsList = (page = 1, pageSize = 10) => {
  getStudentsListApi({
    PageIndex: page,
    PageSize: pageSize,
    ClassesId: props.classesId
  }).then(res => {
    if (res.code === 200) {
      console.log(res.data, "学生列表")
      studentData.value = res.data.items
      // 更新分页信息
      studentPagination.value.current = page
      studentPagination.value.pageSize = pageSize
      studentPagination.value.total = res.data.totalCount || res.data.total || 0
    }
  })
}

// 显示弹窗
const showLearnOfficeModal = () => {
  learnOfficeModalVisible.value = true
  const reqParams = {
    Name: "",
    Account: "",
    Enable: true,
    PageIndex: 1,
    PageSize: 9999
  }
  getLearnOfficeListApi(reqParams).then(res => {
    if (res.code === 200) {
      learnOfficeData.value = res.data.items
    }
  })
}
//显示添加教师弹窗
const addTeacher = () => {
  console.log("打开添加教师弹窗")
  teacherModalVisible.value = true
  // 重置表单数据
  Object.assign(teacherFormData, {
    subId: null,
    teacherName: ""
  })
  getSubjectsListApi().then(res => {
    if (res.code === 200) {
      subjectOptions.value = res.data.map(i => {
        return { value: i.value, label: i.txt }
      })
    }
  })
}

// 查询
const handleSearch = () => {
  // 查询逻辑已通过computed实现，这里可以添加其他逻辑
  const reqParams = {
    ...searchForm,
    Enable: true,
    PageIndex: 1,
    PageSize: 9999
  }
  getLearnOfficeListApi(reqParams).then(res => {
    if (res.code === 200) {
      learnOfficeData.value = res.data.items
    }
  })
  console.log("查询条件:", searchForm)
}

// 重置
const handleReset = () => {
  searchForm.Name = ""
  searchForm.Account = ""
  const reqParams = {
    Name: "",
    Account: "",
    Enable: true,
    PageIndex: 1,
    PageSize: 9999
  }
  getLearnOfficeListApi(reqParams).then(res => {
    if (res.code === 200) {
      learnOfficeData.value = res.data.items
    }
  })
}

// 选择学习官
const selectLearnOffice = record => {
  console.log("选择的学习官数据:", record)

  addOrUpdateLearnOfficeApi({
    userId: record.id,
    classesId: props.classesId
  })
    .then(res => {
      if (res.code === 200) {
        message.success(`成功选择学习官：${record.realName}`)
        learnOfficeModalVisible.value = false
        getLearnOffice()
        // 通知父组件更新树形数据
        emit("updateTree")
      }
    })
    .catch(error => {
      console.error("选择学习官失败:", error)
      // message.error("选择学习官失败")
    })
}

const loData = ref({})
/**
 * 获取班级下的学习官数据
 */
const getLearnOffice = () => {
  getLearnOfficeApi(props.classesId).then(res => {
    if (res.code === 200) {
      loData.value = res.data
      console.log("xuexiguan", loData.value)
    }
  })
}
const tcData = ref([])
/**
 * 获取班级下的老师列表
 */
const getTeacherList = () => {
  getTeacherListApi(props.classesId).then(res => {
    if (res.code === 200) {
      tcData.value = res.data
    }
  })
}
//新增或编辑学习官
// addOrUpdateLearnOfficeApi

//查询所有学习官列表
// getLearnOfficeListApi

// 科目搜索过滤函数
const filterSubjectOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 教师表单提交
const handleTeacherSubmit = async () => {
  try {
    await teacherFormRef.value.validate()
    submitLoading.value = true

    console.log("提交教师表单数据:", teacherFormData)
    addTeacherApi({
      ...teacherFormData,
      classId: props.classesId
    })
      .then(res => {
        if (res.code === 200) {
          message.success("新增成功")
          getTeacherList()
          submitLoading.value = false
          teacherModalVisible.value = false
        }
      })
      .finnaly(() => {
        submitLoading.value = false
      })
  } catch (error) {
    console.log("表单验证失败:", error)
    submitLoading.value = false
  }
}
//删除老师
// const deleteTeacher = i => {
//   deleteTeacherApi(i.id).then(res => {
//     if (res.code === 200) {
//       getTeacherList()
//     }
//   })
// }
// 教师表单取消
const handleTeacherCancel = () => {
  teacherModalVisible.value = false
  teacherFormRef.value?.resetFields()
}

// ==================== 学生管理相关函数 ====================

// 显示添加学生弹窗
const showAddStudentModal = () => {
  addStudentModalVisible.value = true
  // 重置输入列表
  studentInputList.value = [{ key: Date.now(), name: "" }]
}
//导入学生弹窗
const showImportStudentModal = () => {
  importStudentModalVisible.value = true
  // 重置数据
  pasteText.value = ""
  importStudentList.value = []
}

// 添加一行输入
const addStudentInput = () => {
  studentInputList.value.push({
    key: Date.now(),
    name: ""
  })
}

// 删除一行输入
const removeStudentInput = index => {
  if (studentInputList.value.length > 1) {
    studentInputList.value.splice(index, 1)
  } else {
    message.warning("至少保留一行输入框")
  }
}

// 保存学生
const saveStudents = () => {
  // 检查是否有空的姓名
  const emptyItems = studentInputList.value.filter(item => !item.name.trim())

  if (emptyItems.length > 0) {
    message.warning("请填写所有学生姓名，不能有空项")
    return Promise.reject() // 阻止弹窗关闭
  }

  // 获取所有有效的学生姓名
  const validStudents = studentInputList.value.map(item => ({
    ...item,
    name: item.name.trim()
  }))
  console.log(validStudents, "validStudents")
  const reqParams = validStudents.map(item => ({
    // schoolId: 1,
    // classId: 665328045396056,
    realName: item.name
  }))
  console.log(reqParams, "reqParams")
  //   return
  addStudentLoading.value = true
  // // 新增学生
  addStudentApi(
    {
      schoolId: 1,
      classId: props.classesId
    },
    reqParams
  )
    .then(res => {
      if (res.code === 200) {
        getStudentsList(1, studentPagination.value.pageSize) // 添加成功后回到第一页
        message.success(`成功添加`)
        console.log("添加的学生:", validStudents)
        studentInputList.value = [{ key: Date.now(), name: "" }]
        addStudentModalVisible.value = false
      }
    })
    .finally(() => {
      addStudentLoading.value = false
    })

  // 模拟API调用
  //   return new Promise(resolve => {
  //     setTimeout(() => {
  //       // 生成新的学生ID并添加到列表
  //       const maxId = Math.max(...studentData.value.map(item => item.id), 0)
  //       validStudents.forEach((student, index) => {
  //         studentData.value.push({
  //           id: maxId + index + 1,
  //           name: student.name
  //         })
  //       })

  //       addStudentLoading.value = false
  //       message.success(`成功添加 ${validStudents.length} 名学生`)
  //       console.log("添加的学生:", validStudents)

  //       // 重置输入列表
  //       studentInputList.value = [{ key: Date.now(), name: "" }]
  //       resolve()
  //     }, 1000)
  //   })
}

// 取消添加学生
const cancelAddStudent = () => {
  addStudentModalVisible.value = false
  studentInputList.value = [{ key: Date.now(), name: "" }]
}

// 编辑学生
const editStudent = record => {
  editStudentModalVisible.value = true
  currentEditStudent.value = record
  editStudentForm.id = record.id
  editStudentForm.realName = record.realName
}

// 保存编辑的学生
const saveEditStudent = () => {
  if (!editStudentForm.realName.trim()) {
    message.warning("请输入学生姓名")
    return
  }

  editStudentLoading.value = true
  updateStudentApi(editStudentForm)
    .then(res => {
      if (res.code === 200) {
        message.success("修改成功")
        editStudentModalVisible.value = false
        // 获取当前页的数据，保持用户的分页状态
        getStudentsList(studentPagination.value.current, studentPagination.value.pageSize)
      }
    })
    .finally(() => {
      editStudentLoading.value = false
    })
}

// 取消编辑学生
const cancelEditStudent = () => {
  editStudentModalVisible.value = false
  editStudentForm.id = null
  editStudentForm.realName = ""
  currentEditStudent.value = null
}

// 删除学生
const deleteStudent = record => {
  deleteStudentApi([record.id]).then(res => {
    if (res.code === 200) {
      // 删除后保持当前页，如果当前页没有数据了则回到上一页
      const currentPage = studentPagination.value.current
      const pageSize = studentPagination.value.pageSize
      const total = studentPagination.value.total

      // 计算删除后的总页数
      const newTotal = total - 1
      const maxPage = Math.ceil(newTotal / pageSize)

      // 如果当前页超过了最大页数，则回到最大页
      const targetPage = currentPage > maxPage ? Math.max(1, maxPage) : currentPage

      getStudentsList(targetPage, pageSize)
      message.success("删除成功")
    }
  })
}

// 表格改变处理函数（包含分页、排序、筛选）
const handleTableChange = (pagination, filters, sorter) => {
  getStudentsList(pagination.current, pagination.pageSize)
}

// 处理粘贴的文本数据
const handlePasteText = () => {
  if (!pasteText.value.trim()) {
    message.warning("请先粘贴学生姓名数据")
    return
  }

  // 按行分割文本，支持多种分隔符
  const names = pasteText.value
    .split(/[\n\r\t,，]/) // 支持换行、制表符、逗号分割
    .map(name => name.trim())
    .filter(name => name.length > 0)

  if (names.length === 0) {
    message.warning("未检测到有效的姓名数据")
    return
  }

  // 去重处理
  const uniqueNames = [...new Set(names)]
  //   const duplicateCount = names.length - uniqueNames.length

  // 转换为表格数据格式
  const newStudents = uniqueNames.map((name, index) => ({
    key: Date.now() + index,
    name: name
  }))

  importStudentList.value = newStudents

  //   let successMsg = `成功解析 ${uniqueNames.length} 个学生姓名`
  //   if (duplicateCount > 0) {
  //     successMsg += `，已自动去重 ${duplicateCount} 个重复姓名`
  //   }

  //   message.success(successMsg)
}

// 移除导入列表中的学生
const removeImportStudent = record => {
  const index = importStudentList.value.findIndex(item => item.key === record.key)
  if (index > -1) {
    importStudentList.value.splice(index, 1)
  }
}

// 清空粘贴的文本
const clearPasteText = () => {
  pasteText.value = ""
  importStudentList.value = []
}

// 更新导入学生姓名
const updateImportStudentName = (record, newName) => {
  const index = importStudentList.value.findIndex(item => item.key === record.key)
  if (index > -1) {
    importStudentList.value[index].name = newName
  }
}

// 确认导入学生
const confirmImportStudents = () => {
  if (importStudentList.value.length === 0) {
    message.warning("请先添加要导入的学生")
    return
  }

  // 检查是否有空的姓名
  const emptyItems = importStudentList.value.filter(item => !item.name.trim())
  if (emptyItems.length > 0) {
    message.warning("存在空的学生姓名，请检查后重试")
    return
  }

  importStudentLoading.value = true

  // 构造API请求参数
  const reqParams = importStudentList.value.map(item => ({
    realName: item.name.trim()
  }))

  console.log("导入学生参数:", reqParams)

  // 调用添加学生API
  addStudentApi(
    {
      schoolId: 1,
      classId: props.classesId
    },
    reqParams
  )
    .then(res => {
      if (res.code === 200) {
        getStudentsList(1, studentPagination.value.pageSize)
        message.success(`成功导入 ${importStudentList.value.length} 名学生`)
        importStudentModalVisible.value = false
        // 重置数据
        pasteText.value = ""
        importStudentList.value = []
      }
    })
    .catch(error => {
      console.error("导入学生失败:", error)
      message.error("导入学生失败")
    })
    .finally(() => {
      importStudentLoading.value = false
    })
}

// 取消导入学生
const cancelImportStudents = () => {
  importStudentModalVisible.value = false
  pasteText.value = ""
  importStudentList.value = []
}

watch(
  () => props.classesId,
  () => {
    // 获取班级下的学习官数据
    getLearnOffice()
    //获取班级下的老师列表
    getTeacherList()
    // 获取班级下的学生列表
    getStudentsList()
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.class-management-cp {
  .ct {
    margin-bottom: 20px;
    .title {
      margin-bottom: 5px;
      position: relative;
      padding-left: 10px;
    }
    .title:after {
      content: "";
      position: absolute;
      left: 0px;
      top: 40%;
      border-radius: 2px;
      transform: translateY(-40%);
      width: 5px;
      height: 15px;
      background-color: #1890ff;
    }
  }

  // 弹窗样式
  :deep(.ant-modal) {
    .search-area {
      //   background: #f5f5f5;
      padding: 16px;
      margin-bottom: 20px;
      border-radius: 6px;
    }

    .ant-table {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #e6f7ff;
      }
    }
  }

  // 学生管理样式
  .student-operations {
    margin-top: 16px;
    margin-bottom: 8px;
  }

  // 学生表格样式
  .student-table {
    :deep(.ant-table) {
      .ant-table-thead > tr > th {
        background-color: #fafafa;
        font-weight: 600;
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #e6f7ff;
      }
    }

    // 分页样式
    :deep(.ant-pagination) {
      margin-top: 16px;
      text-align: right;

      .ant-pagination-total-text {
        color: #666;
        font-size: 14px;
      }

      .ant-pagination-options {
        .ant-select {
          margin-right: 8px;
        }
      }
    }
  }

  // 添加学生弹窗样式
  .add-student-content {
    .add-student-tip {
      .ant-alert {
        border-radius: 8px;
        border: 1px solid #e6f7ff;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
      }
    }

    .student-input-container {
      max-height: 400px;
      overflow-y: auto;
      padding-right: 4px;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }

    .student-input-row {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      transition: all 0.3s ease;
      gap: 16px;

      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      }

      .input-label {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        min-width: 100px;

        .student-number {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 24px;
          height: 24px;
          background: #1890ff;
          color: white;
          border-radius: 50%;
          font-size: 12px;
          font-weight: 600;
          margin-right: 8px;
        }

        .label-text {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
        }
      }

      .input-control {
        align-items: center;
        gap: 1px;
        flex: 1;
        margin-bottom: 10px;

        .ant-input {
          flex: 1;
          border-radius: 6px;

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .delete-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 6px;
          transition: all 0.3s ease;
          flex-shrink: 0;
          &:hover:not(:disabled) {
            background: #fff2f0;
            color: #ff4d4f;
          }

          &:disabled {
            opacity: 0.3;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  // 导入学生弹窗样式
  .import-student-content {
    .import-student-tip {
      margin-bottom: 16px;

      .ant-alert {
        border-radius: 8px;
        border: 1px solid #e6f7ff;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
      }
    }

    .import-student-text {
      margin-bottom: 16px;
      .ant-input {
        border-radius: 6px;

        &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    .import-student-actions {
      margin-bottom: 16px;
      text-align: right;

      .ant-btn {
        border-radius: 6px;
      }
    }

    .import-student-list {
      .list-header {
        margin-bottom: 12px;
        padding: 8px 12px;
        background: #f5f5f5;
        border-radius: 6px;
        font-weight: 500;
        color: #333;
        border-left: 3px solid #1890ff;

        .header-left {
          span:first-child {
            font-weight: 600;
            color: #1890ff;
          }
        }
      }

      .ant-table {
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e8e8e8;

        .ant-table-thead > tr > th {
          background-color: #fafafa;
          font-weight: 600;
          border-bottom: 1px solid #e8e8e8;

          &:first-child {
            text-align: center;
          }
        }

        .ant-table-tbody > tr {
          &:hover > td {
            background-color: #e6f7ff;
          }

          td {
            padding: 8px 12px;

            &:first-child {
              text-align: center;
              font-weight: 500;
              color: #666;
            }

            .ant-input {
              border: 1px solid #d9d9d9;
              border-radius: 4px;

              &:focus {
                border-color: #40a9ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }

            .ant-btn-link {
              padding: 0;
              height: auto;
              font-size: 12px;

              &.ant-btn-dangerous:hover {
                color: #ff7875;
                background: rgba(255, 77, 79, 0.06);
                border-radius: 4px;
                padding: 2px 6px;
              }
            }
          }
        }

        // 滚动条样式
        .ant-table-body {
          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }
}
</style>
