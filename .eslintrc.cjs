/* eslint-env node */
module.exports = {
  root: true,
  extends: ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/eslint-config-prettier"],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module"
  },
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  ignorePatterns: ["auto-imports.d.ts", "components.d.ts", "dist", "node_modules"],
  globals: {
    // Vue 3 自动导入的全局变量
    ref: "readonly",
    reactive: "readonly",
    computed: "readonly",
    watch: "readonly",
    watchEffect: "readonly",
    onMounted: "readonly",
    onUnmounted: "readonly",
    onUpdated: "readonly",
    onBeforeMount: "readonly",
    onBeforeUnmount: "readonly",
    onBeforeUpdate: "readonly",
    provide: "readonly",
    inject: "readonly",
    nextTick: "readonly",
    defineProps: "readonly",
    defineEmits: "readonly",
    defineExpose: "readonly",
    // Vue Router
    useRouter: "readonly",
    useRoute: "readonly",
    // Pinia
    useUserStore: "readonly",
    // Ant Design Vue
    message: "readonly",
    Modal: "readonly",
    notification: "readonly"
  },
  rules: {
    // 基础规则
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-unused-vars": "warn",
    "no-undef": "error",
    "prefer-const": "warn",
    "no-var": "error",

    // Vue 特定规则
    "vue/multi-word-component-names": "off",
    "vue/no-unused-vars": "warn",
    "vue/no-unused-components": "warn",
    "vue/require-v-for-key": "error",
    "vue/no-use-v-if-with-v-for": "error",

    "prettier/prettier": ["error", { endOfLine: "auto" }]
    // // 代码风格 - 放宽一些规则
    // indent: "off", // 关闭缩进检查，交给 Prettier 处理
    // quotes: "off", // 关闭单双引号校验
    // semi: ["warn", "never"],
    // "comma-dangle": ["warn", "never"],
    // "object-curly-spacing": ["warn", "always"],
    // "array-bracket-spacing": ["warn", "never"],
    // "space-before-function-paren": "off", // 关闭函数括号前空格检查
    // "keyword-spacing": "warn",
    // "space-infix-ops": "warn",
    // "eol-last": "warn",
    // "no-trailing-spaces": "warn",
    // "linebreak-style": ["off"]
  }
}
