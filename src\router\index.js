import { createRouter, createWebHistory, createWebHashHistory } from "vue-router"
import Layout from "@/layout/index.vue"
import { addDynamicRoutes, hasAddedRoutes } from "@/utils/index.js"

const staticRoutes = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: { title: "登录" }
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "Dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        meta: { title: "仪表盘", icon: "DashboardOutlined" }
      }
    ]
  }
  // {
  //   path: "/system",
  //   component: Layout,
  //   meta: { title: "系统管理", icon: "SettingOutlined" },
  //   children: [
  //     {
  //       path: "role",
  //       name: "Role",
  //       component: () => import("@/views/system/role/index.vue"),
  //       meta: { title: "角色管理", icon: "TeamOutlined" }
  //     },
  //     {
  //       path: "menu",
  //       name: "Menu",
  //       component: () => import("@/views/system/menu/index.vue"),
  //       meta: { title: "菜单管理", icon: "MenuOutlined" }
  //     },
  //     {
  //       path: "cloudschool",
  //       name: "CloudSchool",
  //       component: () => import("@/views/system/cloudschool/index.vue"),
  //       meta: { title: "云校管理", icon: "UserOutlined" }
  //     },
  //     {
  //       path: "tasktype",
  //       name: "TaskType",
  //       component: () => import("@/views/system/tasktype/index.vue"),
  //       meta: { title: "任务类型管理", icon: "UserOutlined" }
  //     },
  //     {
  //       path: "dic",
  //       name: "Dic",
  //       component: () => import("@/views/system/dic/index.vue"),
  //       meta: { title: "参数字典管理", icon: "UserOutlined" }
  //     }
  //   ]
  // },
  // 404 页面 - 必须放在最后
]

const router = createRouter({
  // history: createWebHistory(),
  history: createWebHashHistory(),
  routes: staticRoutes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title}`
  }

  const token = localStorage.getItem("token")

  // 如果访问登录页
  if (to.path === "/login") {
    if (token) {
      next("/")
    } else {
      next()
    }
    return
  }

  // 如果没有token，跳转到登录页
  if (!token) {
    next("/login")
    return
  }

  // 如果有token但路由未添加，添加动态路由
  if (!hasAddedRoutes(router)) {
    try {
      const userRoutesData = JSON.parse(localStorage.getItem("userMenu"))
      // 动态添加路由
      addDynamicRoutes(router, userRoutesData)

      // 确保路由添加完成后再导航
      next({ ...to, replace: true })
    } catch (error) {
      console.error("加载路由失败:", error)
      // 如果加载路由失败，清除token并跳转到登录页
      localStorage.removeItem("token")
      next("/login")
    }
  } else {
    next()
  }
})

export default router
