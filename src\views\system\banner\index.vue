<template>
  <div class="banner-management">
    <!-- 操作按钮区域 -->
    <a-card class="operation-card" :bordered="false" style="margin-bottom: 16px">
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd">新增轮播图</a-button>
      </div>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="bannerData"
        :loading="loading"
        row-key="id"
        :pagination="false"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'bannerFileUrl'">
            <a-image
              :src="record.bannerFileUrl"
              :width="100"
              :height="40"
              :preview="true"
              style="object-fit: cover; border-radius: 5px"
              fallback="/logo.png"
            />
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm
                title="确定删除这个轮播图吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" size="small" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑轮播图弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
        <a-form-item label="轮播图名称" name="bannerName">
          <a-input
            v-model:value="formData.bannerName"
            placeholder="请输入轮播图名称"
            maxlength="50"
          />
        </a-form-item>

        <a-form-item label="上传轮播图" name="bannerFileId">
          <div class="banner-upload">
            <!-- 文件上传区域 -->
            <a-upload
              :file-list="uploadFileList"
              :before-upload="beforeUpload"
              :show-upload-list="false"
              accept=".png,.jpg,.jpeg,.gif"
              :disabled="uploadFileList.length >= 1"
            >
              <a-button
                type="dashed"
                style="width: 100%; height: 120px"
                :loading="uploadLoading"
                :disabled="uploadFileList.length >= 1"
              >
                <div style="display: flex; flex-direction: column; align-items: center; gap: 8px">
                  <UploadOutlined style="font-size: 24px" />
                  <!-- <span style="font-size: 14px">点击上传轮播图</span> -->
                  <span style="font-size: 12px; color: #999">
                    支持 PNG、JPG、JPEG、GIF 格式
                    <br />
                    图片尺寸：694x280像素
                    <br />
                    最多上传1张图片
                  </span>
                </div>
              </a-button>
            </a-upload>

            <!-- 上传的图片预览 -->
            <div v-if="uploadFileList.length > 0" class="image-preview">
              <div
                v-for="file in uploadFileList"
                :key="file.uid"
                class="preview-item"
                style="
                  display: inline-block;
                  position: relative;
                  border: 1px solid #d9d9d9;
                  border-radius: 8px;
                  overflow: hidden;
                "
              >
                <img
                  :src="file.url || file.thumbUrl"
                  alt="轮播图预览"
                  style="width: 200px; height: 80px; object-fit: cover; display: block"
                />
                <div
                  class="preview-overlay"
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    transition: opacity 0.3s;
                  "
                  @mouseenter="$event.target.style.opacity = '1'"
                  @mouseleave="$event.target.style.opacity = '0'"
                >
                  <a-button
                    type="primary"
                    size="small"
                    danger
                    @click="handleDeleteFile(file)"
                    style="margin: 0 4px"
                  >
                    删除
                  </a-button>
                </div>
                <div
                  v-if="file.status === 'uploading'"
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.8);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <a-spin />
                </div>
                <div
                  v-if="file.status === 'error'"
                  style="
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 0, 0, 0.1);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #ff4d4f;
                    font-size: 12px;
                  "
                >
                  上传失败
                </div>
              </div>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="Banner">
import { ref, reactive, onMounted, createVNode } from "vue"
import { message, Modal } from "ant-design-vue"
import { UploadOutlined, ExclamationCircleOutlined } from "@ant-design/icons-vue"
import {
  getBannerListApi,
  addBannerApi,
  deleteBannerApi,
  editBannerApi,
  getSysFileConfigApi,
  uploadFileApi,
  deleteFileApi
} from "@/api"

// 响应式数据
const loading = ref(false)
const bannerData = ref([])

// 表格列配置
const columns = [
  {
    title: "id",
    dataIndex: "id",
    key: "id",
    width: 80
  },
  {
    title: "轮播图名称",
    dataIndex: "bannerName",
    key: "bannerName",
    width: 200
  },
  {
    title: "轮播图",
    dataIndex: "bannerFileUrl",
    key: "bannerFileUrl",
    width: 150
  },
  {
    title: "操作",
    key: "action",
    width: 150,
    fixed: "right"
  }
]

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref("新增轮播图")
const submitLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = reactive({
  id: null,
  bannerName: "",
  bannerFileId: null
})

// 编辑模式下的原始文件ID（用于取消时恢复）
const originalFileId = ref(null)

// 表单验证规则
const formRules = {
  bannerName: [
    { required: true, message: "请输入轮播图名称", trigger: "blur" },
    { min: 1, max: 50, message: "轮播图名称长度为1-50个字符", trigger: "blur" }
  ],
  bannerFileId: [{ required: true, message: "请上传轮播图", trigger: "change" }]
}

// 文件上传相关
const uploadLoading = ref(false)
const uploadFileList = ref([])

/**
 * 获取轮播图列表数据
 */
const getBannerList = async () => {
  try {
    loading.value = true
    const res = await getBannerListApi()
    if (res && res.code === 200) {
      bannerData.value = res.data || []
    } else {
      message.error(res?.msg || "获取轮播图列表失败")
    }
  } catch (error) {
    console.error("获取轮播图列表失败:", error)
  } finally {
    loading.value = false
  }
}

/**
 * 新增轮播图
 */
const handleAdd = () => {
  modalTitle.value = "新增轮播图"
  modalVisible.value = true
  clearFormData()
}

/**
 * 编辑轮播图
 */
const handleEdit = record => {
  modalTitle.value = "编辑轮播图"
  modalVisible.value = true

  // 填充表单数据
  formData.id = record.id
  formData.bannerName = record.bannerName
  formData.bannerFileId = record.bannerFileId

  // 保存原始文件ID
  originalFileId.value = record.bannerFileId

  // 如果有图片URL，添加到预览列表
  if (record.bannerFileUrl) {
    uploadFileList.value = [
      {
        uid: Date.now(),
        name: record.bannerName,
        status: "done",
        url: record.bannerFileUrl,
        sysFileId: record.bannerFileId,
        isOriginal: true // 标记为原始文件
      }
    ]
  }
}

/**
 * 删除轮播图
 */
const handleDelete = async record => {
  try {
    const res = await deleteBannerApi([record.id])
    if (res && res.code === 200) {
      message.success("删除成功")
      getBannerList()
    } else {
      message.error(res?.msg || "删除失败")
    }
  } catch (error) {
    console.error("删除轮播图失败:", error)
    message.error("删除失败")
  }
}

/**
 * 表单提交
 */
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    const isEdit = !!formData.id
    const submitData = {
      bannerName: formData.bannerName,
      bannerFileId: formData.bannerFileId
    }

    if (isEdit) {
      submitData.id = formData.id
    }

    const apiCall = isEdit ? editBannerApi : addBannerApi
    const res = await apiCall(submitData)

    if (res && res.code === 200) {
      message.success(isEdit ? "编辑成功" : "新增成功")
      modalVisible.value = false
      getBannerList()
    } else {
      message.error(res?.msg || (isEdit ? "编辑失败" : "新增失败"))
    }
  } catch (error) {
    console.error("提交失败:", error)
  } finally {
    submitLoading.value = false
  }
}

/**
 * 取消弹窗
 */
const handleCancel = () => {
  // 如果是编辑模式且删除了原始图片，需要恢复原始数据
  if (formData.id && originalFileId.value && !formData.bannerFileId) {
    // 用户在编辑时删除了原始图片但取消了编辑，需要刷新列表以恢复显示
    getBannerList()
  }

  modalVisible.value = false
  clearFormData()
}

/**
 * 清空表单数据
 */
const clearFormData = () => {
  formRef.value?.resetFields()
  formData.id = null
  formData.bannerName = ""
  formData.bannerFileId = null
  originalFileId.value = null
  uploadFileList.value = []
}

/**
 * 检查图片尺寸
 */
const checkImageDimensions = file => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => {
      const { width, height } = img
      console.log("img-info", img.width, img.height)

      if (width === 694 && height === 280) {
        resolve(true)
      } else {
        reject(new Error(`图片尺寸必须为694x280像素，当前尺寸为${width}x${height}像素`))
      }
    }
    img.onerror = () => {
      reject(new Error("无法读取图片尺寸"))
    }
    img.src = URL.createObjectURL(file)
  })
}

/**
 * 文件上传前的处理
 */
const beforeUpload = async file => {
  // 检查文件类型
  const validTypes = ["image/png", "image/jpg", "image/jpeg", "image/gif"]
  if (!validTypes.includes(file.type)) {
    message.error("只支持 PNG、JPG、JPEG、GIF 格式的图片")
    return false
  }

  // 检查文件数量
  if (uploadFileList.value.length >= 1) {
    message.error("最多只能上传1张图片")
    return false
  }

  // 检查文件大小（10MB限制）
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    message.error("图片大小不能超过10MB")
    return false
  }

  try {
    // 检查图片尺寸
    await checkImageDimensions(file)
  } catch (error) {
    message.error(error.message)
    return false
  }

  uploadLoading.value = true

  // 添加到文件列表
  const fileItem = {
    uid: file.uid || Date.now() + Math.random(),
    name: file.name,
    status: "uploading",
    size: file.size,
    originFile: file,
    isOriginal: false // 标记为新上传的文件
  }
  uploadFileList.value.push(fileItem)

  try {
    // 获取OSS配置
    const ossConfig = await getOssConfigForFile(file)
    if (!ossConfig || !ossConfig.uploadUrl) {
      throw new Error("无法获取有效的OSS配置")
    }

    // 上传到OSS
    await upload(file, ossConfig.uploadUrl)

    // 获取系统文件ID
    const sysFileId = await getFileId(file, ossConfig.filePath)

    // 更新文件状态
    const index = uploadFileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index > -1) {
      uploadFileList.value[index] = {
        ...uploadFileList.value[index],
        status: "done",
        url: ossConfig.filePath,
        sysFileId: sysFileId
      }
    }

    // 更新表单数据
    formData.bannerFileId = sysFileId
  } catch (error) {
    console.error("文件上传失败:", error)
    message.error(error.message || "文件上传失败")

    // 更新文件状态为失败
    const index = uploadFileList.value.findIndex(item => item.uid === fileItem.uid)
    if (index > -1) {
      uploadFileList.value[index] = {
        ...uploadFileList.value[index],
        status: "error",
        errorMessage: error.message
      }
    }
  } finally {
    uploadLoading.value = false
  }

  return false // 阻止默认上传行为
}

/**
 * 删除上传的文件
 */
const handleDeleteFile = async file => {
  Modal.confirm({
    title: "确认删除文件",
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要删除文件"${file.name}"吗？`,
    okText: "删除",
    cancelText: "取消",
    okType: "danger",
    async onOk() {
      try {
        // 如果是编辑模式下的原始文件，不立即删除服务器文件
        if (formData.id && file.isOriginal) {
          // 编辑模式下删除原始文件，只是从列表中移除，不删除服务器文件
          const index = uploadFileList.value.findIndex(item => item.uid === file.uid)
          if (index > -1) {
            uploadFileList.value.splice(index, 1)
          }
          formData.bannerFileId = null
          message.success("文件已移除")
        } else {
          // 新上传的文件或新增模式下，删除服务器文件
          if (file.status === "done" && file.sysFileId) {
            await deleteFile(file.sysFileId)
            message.success("文件删除成功")
          }

          // 从本地文件列表中删除
          const index = uploadFileList.value.findIndex(item => item.uid === file.uid)
          if (index > -1) {
            uploadFileList.value.splice(index, 1)
          }

          // 清空表单中的文件ID
          formData.bannerFileId = null
        }
      } catch (error) {
        console.error("删除文件失败:", error)
        message.error("删除文件失败")
      }
    }
  })
}

/**
 * 获取文件对应的OSS配置
 */
const getOssConfigForFile = async file => {
  try {
    const fileName = file.name
    const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()

    const res = await getSysFileConfigApi(fileExtension)
    if (res.code === 200) {
      return res.data
    } else {
      throw new Error(`获取OSS配置失败: ${res.msg || "未知错误"}`)
    }
  } catch (error) {
    console.error("获取OSS配置失败:", error)
    throw error
  }
}

/**
 * 上传文件到OSS
 */
const upload = async (file, presignedUrl) => {
  try {
    const arrayBuffer = await file.arrayBuffer()
    const response = await fetch(presignedUrl, {
      method: "PUT",
      body: arrayBuffer,
      headers: {
        "Content-Length": file.size.toString()
      }
    })

    if (!response.ok) {
      const errorText = await response.text().catch(() => "unknown")
      throw new Error(`Upload failed, status: ${response.status}, details: ${errorText}`)
    }

    return response
  } catch (error) {
    console.error("上传到OSS失败:", error)
    throw error
  }
}

/**
 * 获取文件ID
 */
const getFileId = async (file, url) => {
  try {
    const fileName = file.name
    const type = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase()

    const res = await uploadFileApi({
      file_name: fileName,
      file_path: url,
      file_type: type
    })

    if (res.code === 200) {
      return res.data
    } else {
      throw new Error(res?.msg || "文件上传失败")
    }
  } catch (error) {
    console.error("获取文件ID失败:", error)
    throw error
  }
}

/**
 * 删除文件
 */
const deleteFile = async fileId => {
  try {
    const res = await deleteFileApi(fileId)
    if (res.code === 200) {
      return true
    } else {
      throw new Error(res?.msg || "删除文件失败")
    }
  } catch (error) {
    console.error("删除文件失败:", error)
    throw error
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getBannerList()
})
</script>

<style scoped lang="scss">
.banner-management {
  .operation-card {
    .table-operations {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }

  // 上传区域样式
  .banner-upload {
    :deep(.ant-upload) {
      width: 100% !important;
    }

    .image-preview {
      .preview-item {
        &:hover .preview-overlay {
          opacity: 1 !important;
        }
      }
    }
  }

  // 表格样式优化
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }

  // 弹窗样式优化
  :deep(.ant-modal) {
    .ant-form-item-label > label {
      font-weight: 500;
    }
  }
}
</style>
<style lang="scss">
.banner-upload {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .ant-upload-wrapper {
    width: 330px;
  }
  .ant-upload-select {
    width: 100% !important;
  }
}
</style>
