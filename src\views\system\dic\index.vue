<template>
  <div class="dic-management">
    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd"> 新增 </a-button>
    </div>

    <!-- 表格 -->
    <a-table
      :key="tableKey"
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      row-key="id"
      :loading="tableLoading"
      :expand-row-by-click="false"
      :child-row-key="'children'"
      :indent-size="20"
      :expanded-row-keys="expandedRowKeys"
      @expand="onExpand"
      @expandedRowsChange="onExpandedRowsChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'isQuestion'">
          {{ record.isQuestion ? "是" : "否" }}
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleAddChild(record)"> 新增 </a-button>
            <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="参数名称" name="pName">
          <a-input v-model:value="formData.pName" placeholder="请输入参数名称" />
        </a-form-item>

        <a-form-item label="参数枚举值" name="pValue">
          <a-input-number
            v-model:value="formData.pValue"
            placeholder="请输入参数枚举值"
            :min="1"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number
            v-model:value="formData.sort"
            placeholder="请输入排序值"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="父级参数" name="pid">
          <a-input v-model:value="parentName" placeholder="父级参数" disabled />
        </a-form-item>
        <!-- {{ formData.nextischeck }} -->
        <a-form-item label="下级是否为选项" name="nextischeck">
          <a-select
            v-model:value="formData.nextischeck"
            placeholder="请选择下级是否为选项"
            style="width: 100%"
            allow-clear
          >
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <!-- {{ formData.isRequiredSelection }} -->
        <a-form-item label="是否必填" name="isRequiredSelection" v-if="formData.nextischeck">
          <a-select
            v-model:value="formData.isRequiredSelection"
            placeholder="请选择是否必填"
            style="width: 100%"
            allow-clear
          >
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <!-- {{ formData.selectionMode }}- -->
        <a-form-item label="选项类型" name="selectionMode" v-if="formData.nextischeck">
          <a-select
            v-model:value="formData.selectionMode"
            placeholder="请选择选项类型"
            style="width: 100%"
            allow-clear
          >
            <a-select-option :value="1">单选</a-select-option>
            <a-select-option :value="2">多选</a-select-option>
          </a-select>
        </a-form-item>
        <!-- {{ formData.childrenIsOption }}-123 -->

        <a-form-item
          label="是否纳入解决方案问题记录"
          name="isQuestion"
          v-if="formData.childrenIsOption"
        >
          <!-- <a-select v-model:value="formData.isQuestion" placeholder="请选择是否问题" /> -->
          <a-select
            v-model:value="formData.isQuestion"
            placeholder="请选择是否纳入解决方案问题记录"
            style="width: 100%"
            allow-clear
          >
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <!-- {{ formData.questionType }} -->

        <a-form-item
          label="问题类型"
          name="questionType"
          v-if="formData.childrenIsOption && formData.isQuestion"
        >
          <a-select
            v-model:value="formData.questionType"
            :options="options"
            placeholder="请选择问题类型"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="Dic">
import {
  addDicApi,
  editDicApi,
  deleteDicApi,
  getDicListApi,
  getDicTreeApi,
  getQuestionTypeListApi,
  getDicDetailApi
} from "@/api/index.js"

// 响应式数据
const modalVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const tableLoading = ref(false)
const submitLoading = ref(false)
const deleteLoading = ref(false)
const tableKey = ref(0) // 用于强制重新渲染表格

// 表单数据
const formData = reactive({
  id: null,
  pName: "",
  pValue: null,
  pid: 0,
  sort: null, // 修改默认值为1

  // 下级是否为选项，此字段不参与后端交互
  nextischeck: false,
  // 是否必填
  isRequiredSelection: null,
  // 选项类型
  selectionMode: null,

  // 是否纳入解决方案问题记录
  isQuestion: false,
  // 问题类型
  questionType: null
})
/**
 * 问题类型数据
 */
const options = ref([])
watch(
  () => modalVisible.value,
  val => {
    if (val) {
      getQuestionTypeListApi().then(res => {
        if (res.code === 200) {
          options.value = (res.data || []).map(i => ({
            label: i.text,
            value: i.value
          }))
        }
      })
    }
  }
)
// 父级参数显示名称
const parentName = ref("")

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    console.log("分页变化:", { page, pageSize })
    pagination.current = page
    pagination.pageSize = pageSize
    // 分页操作时重置展开状态
    resetExpandedState()
    getDicList()
  },
  onShowSizeChange: (current, size) => {
    console.log("页面大小变化:", { current, size })
    pagination.current = 1
    pagination.pageSize = size
    // 修改页码大小时重置展开状态
    resetExpandedState()
    getDicList()
  }
})

// 表格列配置
const columns = [
  {
    title: "参数名称",
    dataIndex: "pName",
    key: "pName"
  },
  {
    title: "排序",
    dataIndex: "sort",
    key: "sort"
  },
  {
    title: "ID",
    dataIndex: "id",
    key: "id"
    // width: 80
  },
  {
    title: "参数枚举值",
    dataIndex: "pValue",
    key: "pValue"
  },
  // 是否纳入解决方案问题记录
  {
    title: "是否纳入解决方案问题记录",
    dataIndex: "isQuestion",
    key: "isQuestion"
  },
  {
    title: "操作",
    key: "action",
    width: 200
  }
]

// 表格数据
const dataSource = ref([])

// 展开状态管理
const expandedRowKeys = ref([]) // 存储当前展开的行的key

// 表单验证规则
const rules = {
  pName: [{ required: true, message: "请输入参数名称", trigger: "blur" }],
  pValue: [
    { required: true, message: "请输入参数枚举值", trigger: "blur" },
    { type: "number", min: 1, message: "参数枚举值必须大于等于1", trigger: "blur" }
  ],
  sort: [
    { required: true, message: "请输入排序值", trigger: "blur" },
    { type: "number", min: 0, message: "排序值必须大于等于0", trigger: "blur" }
  ],
  isRequiredSelection: [{ required: true, message: "请选择是否必填", trigger: "change" }],
  selectionMode: [{ required: true, message: "请选择选项类型", trigger: "change" }],
  isQuestion: [{ required: true, message: "请选择是否问题", trigger: "change" }],
  questionType: [{ required: true, message: "请选择问题类型", trigger: "change" }]
}

/**
 * 刷新表格数据并保持展开状态
 */
const refreshTableWithExpandState = async () => {
  console.log("开始刷新表格数据，当前展开状态:", expandedRowKeys.value)

  try {
    // 保存当前展开状态
    const savedExpandedKeys = [...expandedRowKeys.value]
    console.log("保存的展开状态:", savedExpandedKeys)

    // 重新获取数据
    await getDicList()

    // 数据获取成功后，需要重新加载已展开节点的子数据
    if (savedExpandedKeys.length > 0) {
      console.log("开始恢复展开状态和子数据")
      await restoreExpandedState(savedExpandedKeys)
    }

    console.log("表格数据刷新完成，展开状态已保持")
  } catch (error) {
    console.error("刷新表格数据失败:", error)
  }
}

/**
 * 恢复展开状态和子数据
 */
const restoreExpandedState = async savedExpandedKeys => {
  console.log("开始恢复展开状态:", savedExpandedKeys)

  // 首先设置展开状态
  expandedRowKeys.value = [...savedExpandedKeys]

  // 然后为每个展开的节点重新加载子数据
  for (const nodeId of savedExpandedKeys) {
    try {
      console.log("为节点重新加载子数据:", nodeId)

      // 查找对应的节点
      const findNode = (nodes, targetId) => {
        for (const node of nodes) {
          if (node.id === targetId) {
            return node
          }
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children, targetId)
            if (found) return found
          }
        }
        return null
      }

      const targetNode = findNode(dataSource.value, nodeId)
      if (!targetNode) {
        console.log("未找到目标节点:", nodeId)
        continue
      }

      // 如果节点没有子数据，重新加载
      if (!targetNode.children || targetNode.children.length === 0) {
        console.log("重新加载节点子数据:", nodeId)
        const res = await getDicTreeApi(nodeId)

        if (res && res.code === 200) {
          const childrenData = res.data || []

          if (childrenData.length > 0) {
            const processedChildren = childrenData.map(child => ({
              ...child,
              hasChildren: false,
              children: []
            }))

            // 更新数据源中对应节点的children
            const updateNodeChildren = (nodes, targetId, children) => {
              return nodes.map(node => {
                if (node.id === targetId) {
                  return { ...node, children: [...children] }
                }
                if (node.children && node.children.length > 0) {
                  return {
                    ...node,
                    children: updateNodeChildren(node.children, targetId, children)
                  }
                }
                return node
              })
            }

            dataSource.value = updateNodeChildren(dataSource.value, nodeId, processedChildren)
            console.log("节点子数据已恢复:", nodeId)
          }
        }
      } else {
        console.log("节点已有子数据，跳过加载:", nodeId)
      }
    } catch (error) {
      console.error("恢复节点子数据失败:", nodeId, error)
    }
  }

  console.log("展开状态恢复完成")
}

/**
 * 获取参数字典列表数据
 */
const getDicList = async () => {
  // 如果正在加载中，直接返回，避免重复调用
  if (tableLoading.value) {
    console.log("正在加载中，跳过重复调用")
    return
  }

  try {
    tableLoading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize
    }

    console.log("调用参数字典列表接口，参数:", params)
    const res = await getDicListApi(params)
    console.log("参数字典列表数据:", res)

    if (res && res.code === 200) {
      // 为根节点数据添加可展开状态
      const items = (res.data.items || []).map(item => ({
        ...item,
        hasChildren: true, // 假设所有根节点都可能有子节点
        children: [] // 初始化空的children数组
      }))

      dataSource.value = items
      pagination.total = res.data.total || 0
      console.log("表格数据已更新，条数:", items.length)
    } else {
      message.error(res?.msg || "获取数据失败")
      dataSource.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error("获取参数字典列表失败:", error)
    dataSource.value = []
    pagination.total = 0
    throw error // 重新抛出错误，让resetTableExpandState能够捕获
  } finally {
    tableLoading.value = false
  }
}

// 顶部新增按钮 - 新增顶级节点
const handleAdd = () => {
  modalTitle.value = "新增顶级参数"
  modalVisible.value = true
  parentName.value = "根节点"
  Object.assign(formData, {
    id: null,
    pName: "",
    pValue: null,
    pid: 0,
    sort: null, // 设置默认排序值为1
    // 下级是否为选项，此字段不参与后端交互
    nextischeck: false,

    isRequiredSelection: null,
    selectionMode: null,
    isQuestion: false,
    questionType: null,
    childrenIsOption: false

    // 下级是否为选项，此字段不参与后端交互
    // nextischeck: false,
    // // 是否必填
    // isRequiredSelection: null,
    // // 选项类型
    // selectionMode: null,

    // // 是否纳入解决方案问题记录
    // isQuestion: false,
    // // 问题类型
    // questionType: null
  })
}

// 新增子项
const handleAddChild = record => {
  console.log("新增", record)

  modalTitle.value = "新增子参数"
  modalVisible.value = true
  parentName.value = record.pName
  Object.assign(formData, {
    id: null,
    pName: "",
    pValue: null,
    pid: record.id,
    sort: null, // 设置默认排序值为1
    isQuestion: false,
    questionType: null,
    childrenIsOption: record.childrenIsOption
  })
}

// 编辑
const handleEdit = async record => {
  console.log("编辑", record)

  modalTitle.value = "编辑参数"
  modalVisible.value = true
  // 查找父级名称
  if (record.pid === 0) {
    parentName.value = "根节点"
  } else {
    // 在整个数据源中查找父级（包括子节点）
    const findParent = (data, targetPid) => {
      for (const item of data) {
        if (item.id === targetPid) {
          return item
        }
        if (item.children && item.children.length > 0) {
          const found = findParent(item.children, targetPid)
          if (found) return found
        }
      }
      return null
    }

    const parentRecord = findParent(dataSource.value, record.pid)
    parentName.value = parentRecord ? parentRecord.pName : `父级ID: ${record.pid}`
  }
  const res = await getDicDetailApi(record.id)
  if (res.code === 200) {
    const data = res.data
    Object.assign(formData, {
      id: data.id,
      pName: data.pName,
      pValue: data.pValue,
      pid: data.pid,
      sort: data.sort || 0,
      // nextischeck下级是否为选项，此字段不参与后端交互,编辑时判断选项类型是1或者2来设置此值
      nextischeck: [1, 2].includes(data.selectionMode),
      // 是否必填
      isRequiredSelection: data.isRequiredSelection,
      // 选项类型
      selectionMode: data.selectionMode === 0 ? null : data.selectionMode,

      childrenIsOption: data.parent?.childrenIsOption,
      // 是否纳入解决方案问题记录
      isQuestion: data.isQuestion,
      // 问题类型
      questionType: data.questionType ? data.questionType : null
    })
  }
}

// 删除单条记录
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除参数 "${record.pName}" 吗？`,
    confirmLoading: deleteLoading.value,
    async onOk() {
      try {
        deleteLoading.value = true
        const res = await deleteDicApi(record.id)

        if (res && res.code === 200) {
          message.success("删除成功")
          // 刷新表格数据并保持展开状态
          refreshTableWithExpandState()
        } else {
          message.error(res?.msg || "删除失败")
        }
      } catch (error) {
        console.error("删除失败:", error)
      } finally {
        deleteLoading.value = false
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    console.log("表单数据", formData)

    submitLoading.value = true

    const params = {
      pid: formData.pid,
      pName: formData.pName,
      pValue: formData.pValue,
      sort: formData.sort, // 用户输入的排序值
      isQuestion: formData.isQuestion,
      questionType: formData.questionType,
      selectionMode: formData.selectionMode,
      isRequiredSelection: formData.isRequiredSelection
    }
    // 是否纳入解决方案问题记录为false时，删除问题类型字段
    if (!formData.isQuestion) {
      delete params.questionType
    }
    if (!formData.nextischeck) {
      delete params.selectionMode
      delete params.isRequiredSelection
    }
    console.log("提交参数:", params)
    if (formData.id) {
      // 编辑 - 添加ID参数
      const editParams = {
        id: formData.id,
        ...params
      }
      const res = await editDicApi(editParams)
      if (res && res.code === 200) {
        message.success("更新成功")
        handleCancel()
        // 刷新表格数据并保持展开状态
        refreshTableWithExpandState()
      } else {
        message.error(res?.msg || "更新失败")
      }
    } else {
      // if (!params.childrenIsOption) {
      //   delete params.isQuestion
      // }
      // 新增
      const res = await addDicApi(params)
      if (res && res.code === 200) {
        message.success("新增成功")
        handleCancel()
        // 刷新表格数据并保持展开状态
        refreshTableWithExpandState()
      } else {
        message.error(res?.msg || "新增失败")
      }
    }
  } catch (error) {
    console.log("表单验证失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  // 重置提交loading状态
  submitLoading.value = false
  formRef.value?.resetFields()
}

// 组件挂载时获取数据
onMounted(() => {
  console.log("参数字典管理组件已挂载，开始获取数据")
  // 初始加载时重置展开状态
  resetExpandedState()
  getDicList()
})

// 展开状态变化处理
const onExpandedRowsChange = expandedKeys => {
  console.log("展开状态变化:", expandedKeys)
  expandedRowKeys.value = [...expandedKeys]
}

/**
 * 重置展开状态为收起
 */
const resetExpandedState = () => {
  console.log("重置展开状态为收起")
  expandedRowKeys.value = []
}

// 展开行事件处理
const onExpand = async (expanded, record) => {
  console.log("展开事件触发:", { expanded, recordId: record.id })

  // 如果是收起操作，直接返回
  if (!expanded) {
    console.log("收起操作，直接返回")
    return
  }

  // 如果已经有子数据且不为空，直接返回
  if (record.children && record.children.length > 0) {
    console.log("子数据已存在，直接返回")
    return
  }

  console.log("开始懒加载子数据，父节点ID:", record.id)

  try {
    console.log("调用getDicTreeApi获取子数据...")
    const res = await getDicTreeApi(record.id)
    console.log("获取到的子数据:", res)

    if (res && res.code === 200) {
      const childrenData = res.data || []

      if (childrenData.length === 0) {
        // message.info("该节点暂无子数据")
        return
      }

      // 为子数据也添加可展开状态（如果需要的话）
      const processedChildren = childrenData.map(child => ({
        ...child,
        hasChildren: false, // 假设子节点暂时不支持再次展开，可根据实际需求调整
        children: []
      }))

      // 更新数据源中对应节点的children
      const updateNodeChildren = (nodes, targetId, children) => {
        return nodes.map(node => {
          if (node.id === targetId) {
            console.log(`更新节点 ${targetId} 的子数据`)
            return { ...node, children: [...children] }
          }
          if (node.children && node.children.length > 0) {
            return { ...node, children: updateNodeChildren(node.children, targetId, children) }
          }
          return node
        })
      }

      dataSource.value = updateNodeChildren(dataSource.value, record.id, processedChildren)
      console.log("懒加载完成，当前数据源:", dataSource.value)
    } else {
      message.error(res?.msg || "获取子数据失败")
    }
  } catch (error) {
    console.error("加载子数据失败:", error)
  }
}
</script>

<style lang="scss" scoped>
.dic-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}
</style>
