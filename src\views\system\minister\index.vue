<!--部长管理 -->
<template>
  <div class="minister-management">
    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd">新增</a-button>
    </div>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="ministerData"
      :pagination="pagination"
      row-key="id"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'imImport'">
          <a-tag v-if="record.imImport" color="success" style="font-size: 12px">已导入</a-tag>
          <div v-else>
            <a-tag color="#ccc" style="font-size: 12px">未导入</a-tag>
            <a-button type="primary" style="font-size: 12px" size="small" @click="importFn(record)"
              >导入</a-button
            >
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" size="small" @click="handleResetPassword(record)"
              >修改密码</a-button
            >
            <a-popconfirm
              title="确定删除吗?"
              ok-text="是"
              cancel-text="否"
              @confirm="handleDelete(record)"
            >
              <a href="#" style="color: #ff4d4f">删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-spin :spinning="detailLoading" tip="正在获取详情数据...">
        <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
          <a-form-item label="姓名" name="name">
            <a-input v-model:value="formData.name" placeholder="请输入姓名" maxlength="20" />
          </a-form-item>

          <a-form-item label="账号" name="account">
            <a-input
              v-model:value="formData.account"
              placeholder="请输入账号"
              maxlength="30"
              :disabled="modalTitle == '编辑部长'"
            />
          </a-form-item>

          <a-form-item label="电话" name="phone">
            <a-input v-model:value="formData.phone" placeholder="请输入电话号码" maxlength="11" />
          </a-form-item>

          <!-- 新增时显示密码输入框 -->
          <a-form-item v-if="!currentRecord" label="密码" name="password">
            <a-input-password
              v-model:value="formData.password"
              placeholder="请输入密码"
              maxlength="20"
            />
          </a-form-item>

          <a-form-item label="管理云校" name="cloudSchoolId">
            <a-select
              v-model:value="formData.cloudSchoolId"
              placeholder="请选择管理云校"
              style="width: 100%"
              show-search
              :filter-option="filterOption"
            >
              <a-select-option
                v-for="school in cloudSchoolOptions"
                :key="school.value"
                :value="school.value"
                :label="school.label"
              >
                {{ school.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </a-spin>
    </a-modal>

    <!-- 修改密码弹窗 -->
    <a-modal
      v-model:open="passwordModalVisible"
      title="修改密码"
      width="400px"
      :confirm-loading="passwordSubmitLoading"
      @ok="handlePasswordSubmit"
      @cancel="handlePasswordCancel"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordFormData"
        :rules="passwordRules"
        layout="vertical"
      >
        <a-form-item label="新密码" name="newPassword">
          <a-input-password
            v-model:value="passwordFormData.newPassword"
            placeholder="请输入新密码"
            maxlength="20"
          />
        </a-form-item>

        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password
            v-model:value="passwordFormData.confirmPassword"
            placeholder="请再次输入新密码"
            maxlength="20"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 导入IM弹窗 -->
    <a-modal
      v-model:open="importModalVisible"
      title="导入IM"
      width="400px"
      :confirm-loading="importSubmitLoading"
      @ok="handleImportSubmit"
      @cancel="handleImportCancel"
    >
      <a-form ref="importFormRef" :model="importFormData" layout="vertical">
        <a-form-item label="昵称" name="nick" required>
          <a-input v-model:value="importFormData.nick" placeholder="请输入昵称" maxlength="20" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="Minister">
import { ref, reactive, onMounted, watch } from "vue"
import { message } from "ant-design-vue"
import {
  addMinisterUserApi,
  deleteMinisterUserApi,
  updateMinisterUserApi,
  getMinisterDetailApi,
  getMinisterUserListApi,
  updateMinisterUserPwdApi,
  getCloudSchoolDropdownListApi,
  importMinisterImApi
} from "@/api"
// addMinisterUserApi({
//   account: "string",
//   password: "string",
//   realName: "string",
//   phone: "string",
//   cloudSchoolId: 0
// })
// 表格列配置
const columns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id"
  },
  {
    title: "姓名",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "账号",
    dataIndex: "account",
    key: "account"
  },
  {
    title: "电话",
    dataIndex: "phone",
    key: "phone"
  },
  {
    title: "管理云校",
    dataIndex: "cloudSchoolName",
    key: "cloudSchoolName"
  },
  {
    title: "IM状态",
    dataIndex: "imImport",
    key: "imImport"
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right"
  }
]
/**
 * 导入
 */
const importFormRef = ref()
const importSubmitLoading = ref(false)
const importModalVisible = ref(false)
const importFormData = ref({
  nick: undefined,
  userID: undefined
})
const importFn = record => {
  console.log(record)
  importFormData.value.userID = record.id
  importModalVisible.value = true
}
/**
 * 导入IM
 */
const handleImportSubmit = async () => {
  await importFormRef.value.validate()
  importSubmitLoading.value = true
  const reqParams = [importFormData.value]
  importMinisterImApi(reqParams)
    .then(res => {
      if (res.code === 200) {
        message.success("导入成功")
        importModalVisible.value = false
        getMinisterList()
      } else {
        message.error(res.msg || "导入失败")
      }
    })
    .finally(() => {
      importSubmitLoading.value = false
    })
}
const handleImportCancel = () => {
  importFormData.value = {
    nick: undefined,
    userID: undefined
  }
  importModalVisible.value = false
}
// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref("")
const submitLoading = ref(false)
const detailLoading = ref(false) // 获取详情时的加载状态
const formRef = ref()
const currentRecord = ref(null)

// 修改密码相关数据
const passwordModalVisible = ref(false)
const passwordSubmitLoading = ref(false)
const passwordFormRef = ref()
const currentPasswordRecord = ref(null)

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    getMinisterList()
  },
  onShowSizeChange: (current, size) => {
    pagination.current = 1
    pagination.pageSize = size
    getMinisterList()
  }
})

// 表单数据
const formData = reactive({
  id: null,
  name: "",
  account: "",
  phone: "",
  password: "",
  cloudSchoolId: null
})

// 修改密码表单数据
const passwordFormData = reactive({
  newPassword: "",
  confirmPassword: ""
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入姓名", trigger: "blur" },
    { min: 2, max: 20, message: "姓名长度为2-20个字符", trigger: "blur" }
  ],
  account: [
    { required: true, message: "请输入账号", trigger: "blur" },
    { min: 3, max: 30, message: "账号长度为3-30个字符", trigger: "blur" }
  ],
  phone: [
    { required: true, message: "请输入电话号码", trigger: "blur" },
    { pattern: /^[0-9]{11}$/, message: "请输入11位数字", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度为6-20个字符", trigger: "blur" }
  ],
  cloudSchoolId: [{ required: true, message: "请选择管理云校", trigger: "change" }]
}

// 修改密码表单验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度为6-20个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    {
      validator: (rule, value) => {
        if (value && value !== passwordFormData.newPassword) {
          return Promise.reject("两次输入的密码不一致")
        }
        return Promise.resolve()
      },
      trigger: "blur"
    }
  ]
}

// 部长数据
const ministerData = ref([])

/**
 * 获取部长列表数据
 */
const getMinisterList = async () => {
  // 如果正在加载中，直接返回，避免重复调用
  if (loading.value) {
    console.log("正在加载中，跳过重复调用")
    return
  }

  try {
    loading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize
    }

    console.log("调用部长列表接口，参数:", params)
    const res = await getMinisterUserListApi(params)
    console.log("部长列表数据:", res)

    if (res && res.code === 200) {
      // 处理返回的数据，确保字段映射正确
      ministerData.value = (res.data.items || []).map(item => ({
        id: item.id,
        name: item.realName || item.name, // 后端可能返回realName字段
        account: item.account,
        phone: item.phone,
        cloudSchoolId: item.cloudSchoolId,
        cloudSchoolName: item.cloudSchoolName || "未分配",
        imImport: item.imImport
      }))
      // 更新分页总数
      pagination.total = res.data.total || 0
    } else {
      ministerData.value = []
      pagination.total = 0
      if (res && res.msg) {
        message.error(res.msg)
      }
    }
  } catch (error) {
    console.error("获取部长列表失败:", error)
    // message.error("获取部长列表失败")
    ministerData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

/**
 * 获取云校选项数据
 */
const loadCloudSchoolOptions = async () => {
  try {
    const res = await getCloudSchoolDropdownListApi()
    if (res.code === 200) {
      cloudSchoolOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error("获取云校选项失败:", error)
  }
}

// 监听弹窗打开，加载云校选项
watch(
  () => modalVisible.value,
  val => {
    if (val) {
      loadCloudSchoolOptions()
    }
  }
)

// 云校选项数据
const cloudSchoolOptions = ref([])

// 搜索过滤函数
const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 新增部长
const handleAdd = () => {
  modalTitle.value = "新增部长"
  modalVisible.value = true
  currentRecord.value = null
  // 重置表单数据
  Object.assign(formData, {
    id: null,
    name: "",
    account: "",
    phone: "",
    password: "",
    cloudSchoolId: null
  })
}

// 编辑部长
const handleEdit = async record => {
  try {
    modalTitle.value = "编辑部长"
    modalVisible.value = true
    currentRecord.value = record
    detailLoading.value = true // 使用专门的详情加载状态

    console.log("获取部长详情:", record.id)
    const res = await getMinisterDetailApi(record.id)
    console.log("部长详情数据:", res)

    if (res && res.code === 200) {
      // 使用接口返回的详细数据填充表单
      Object.assign(formData, {
        id: res.data.id,
        name: res.data.realName || res.data.name,
        account: res.data.account,
        phone: res.data.phone,
        password: "", // 编辑时密码字段为空，不显示
        cloudSchoolId: res.data.cloudSchoolId
      })
    } else {
      message.error(res?.msg || "获取部长详情失败")
      // 如果获取详情失败，使用表格数据作为备用
      Object.assign(formData, {
        id: record.id,
        name: record.name,
        account: record.account,
        phone: record.phone,
        password: "",
        cloudSchoolId: record.cloudSchoolId
      })
    }
  } catch (error) {
    console.error("获取部长详情失败:", error)
    // 如果获取详情失败，使用表格数据作为备用
    Object.assign(formData, {
      id: record.id,
      name: record.name,
      account: record.account,
      phone: record.phone,
      password: "",
      cloudSchoolId: record.cloudSchoolId
    })
  } finally {
    detailLoading.value = false
  }
}

// 修改密码
const handleResetPassword = record => {
  console.log("修改密码:", record)
  passwordModalVisible.value = true
  currentPasswordRecord.value = record
  // 重置密码表单数据
  Object.assign(passwordFormData, {
    newPassword: "",
    confirmPassword: ""
  })
}

// 删除部长
const handleDelete = async record => {
  try {
    console.log("删除部长:", record)
    const res = await deleteMinisterUserApi(record.id)

    if (res && res.code === 200) {
      message.success("删除成功")

      // 计算删除后当前页是否还有数据
      const currentPageDataCount = ministerData.value.length
      const isLastItemOnPage = currentPageDataCount === 1
      const isNotFirstPage = pagination.current > 1

      // 如果删除的是当前页最后一条数据，且不是第一页，则跳转到前一页
      if (isLastItemOnPage && isNotFirstPage) {
        pagination.current = pagination.current - 1
      }
      console.log("删除后当前页:", pagination.current)
      // 重新获取列表数据
      getMinisterList()
    } else {
      message.error(res?.msg || "删除失败")
    }
  } catch (error) {
    console.error("删除部长失败:", error)
    // message.error("删除失败")
  }
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    const isEdit = !!currentRecord.value

    // 准备提交的数据
    const submitData = {
      account: formData.account,
      realName: formData.name, // 注意：后端接口使用realName字段
      phone: formData.phone,
      cloudSchoolId: formData.cloudSchoolId
    }

    // 新增时需要密码，编辑时需要id
    if (isEdit) {
      submitData.id = formData.id
    } else {
      submitData.password = formData.password
    }

    console.log("提交表单数据:", submitData)

    let res
    if (isEdit) {
      // 调用编辑接口
      res = await updateMinisterUserApi(submitData)
    } else {
      // 调用新增接口
      res = await addMinisterUserApi(submitData)
    }

    if (res && res.code === 200) {
      const successMessage = isEdit ? "修改成功" : "新增成功"
      message.success(successMessage)
      modalVisible.value = false
      // 新增成功后回到第一页，编辑成功后保持当前页
      if (!isEdit) {
        pagination.current = 1
      }
      // 重新获取列表数据
      getMinisterList()
    } else {
      message.error(res?.msg || (isEdit ? "修改失败" : "新增失败"))
    }
  } catch (error) {
    console.error("提交失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消弹窗
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}

// 修改密码表单提交
const handlePasswordSubmit = async () => {
  try {
    await passwordFormRef.value.validate()
    passwordSubmitLoading.value = true

    const submitData = {
      id: currentPasswordRecord.value.id,
      newPassword: passwordFormData.newPassword
    }

    console.log("密码修改数据:", submitData)

    const res = await updateMinisterUserPwdApi(submitData)

    if (res && res.code === 200) {
      message.success("密码修改成功")
      passwordModalVisible.value = false
      // 重置表单数据
      Object.assign(passwordFormData, {
        newPassword: "",
        confirmPassword: ""
      })
      currentPasswordRecord.value = null
    } else {
      message.error(res?.msg || "密码修改失败")
    }
  } catch (error) {
    console.error("密码修改失败:", error)
  } finally {
    passwordSubmitLoading.value = false
  }
}

// 取消修改密码弹窗
const handlePasswordCancel = () => {
  passwordModalVisible.value = false
  passwordFormRef.value?.resetFields()
  // 重置表单数据
  Object.assign(passwordFormData, {
    newPassword: "",
    confirmPassword: ""
  })
  currentPasswordRecord.value = null
}

// 组件挂载时获取数据
onMounted(() => {
  console.log("部长管理组件已加载")
  getMinisterList()
})
</script>

<style lang="scss" scoped>
.minister-management {
  padding: 20px;
  background: #fff;
  border-radius: 8px;

  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }

  // 表格样式优化
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }

  // 弹窗样式优化
  :deep(.ant-modal) {
    .ant-form-item-label > label {
      font-weight: 500;
    }

    // 密码输入框样式
    .ant-input-password {
      .ant-input {
        border-radius: 6px;

        &:focus {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    // 表单验证错误提示样式
    .ant-form-item-explain-error {
      font-size: 12px;
      margin-top: 4px;
    }
  }
}
</style>
