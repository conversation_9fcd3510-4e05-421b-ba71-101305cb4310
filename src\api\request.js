import axios from "axios"
import { message } from "ant-design-vue"

// 从环境变量获取 API 基础地址
const baseURL = import.meta.env.VITE_API_BASE_URL

console.log("当前环境:", import.meta.env.MODE)
console.log("API地址:", baseURL)
console.log("应用标题:", import.meta.env.VITE_APP_TITLE)

// 创建axios实例
const request = axios.create({
  baseURL, // 基础URL
  timeout: 10000, // 请求超时时间
  headers: {
    "Content-Type": "application/json"
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加token到请求头
    const token = localStorage.getItem("token")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response
    // console.log("响应拦截器参数---->", response)
    // data = {
    //   Data: null,
    //   msg: "账号或密码错误！",
    //   code: 1000
    // }
    // 根据后端返回的数据结构进行处理
    if (data.code === 200) {
      return data
    } else {
      message.error(data.msg || "请求失败")
      return Promise.reject(new Error(data.msg || "请求失败"))
    }
  },
  error => {
    let errorMessage = "网络错误"

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 401:
          errorMessage = "未授权，请重新登录"
          // 清除token并跳转到登录页
          localStorage.removeItem("token")
          window.location.href = "/login"
          break
        case 403:
          errorMessage = "拒绝访问"
          break
        case 404:
          errorMessage = "请求地址不存在"
          break
        case 500:
          errorMessage = "服务器内部错误"
          break
        default:
          errorMessage = data?.msg || `连接错误${status}`
      }
    } else if (error.request) {
      errorMessage = "网络连接异常"
    } else {
      errorMessage = error.msg
    }

    message.error(errorMessage)
    return Promise.reject(error)
  }
)

export default request
