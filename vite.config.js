import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import { resolve } from "path"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { AntDesignVueResolver } from "unplugin-vue-components/resolvers"

export default defineConfig({
  plugins: [
    vue(),
    // 自动导入Vue3 API
    AutoImport({
      imports: [
        "vue",
        "vue-router",
        "pinia",
        {
          "ant-design-vue": ["message", "Modal", "notification"]
        }
      ],
      dts: true, // 生成类型声明文件
      eslintrc: {
        enabled: true // 生成eslint配置
      }
    }),
    // 自动导入组件
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false // css in js
        })
      ],
      dts: true // 生成类型声明文件
    })
  ],
  build: {
    chunkSizeWarningLimit: 500, // 允许最大 chunk 大小（默认 500kb）
    rollupOptions: {
      output: {
        // 手动分割代码
        manualChunks: id => {
          // Vue 核心库
          if (id.includes("vue") && !id.includes("ant-design-vue")) {
            return "vue-vendor"
          }

          // Ant Design Vue 按功能分割
          if (id.includes("ant-design-vue")) {
            // 表格相关组件
            if (id.includes("/table/") || id.includes("/pagination/")) {
              return "antd-table"
            }
            // 表单相关组件
            if (
              id.includes("/form/") ||
              id.includes("/input/") ||
              id.includes("/select/") ||
              id.includes("/cascader/") ||
              id.includes("/date-picker/") ||
              id.includes("/upload/")
            ) {
              return "antd-form"
            }
            // 布局相关组件
            if (id.includes("/layout/") || id.includes("/menu/") || id.includes("/breadcrumb/")) {
              return "antd-layout"
            }
            // 反馈相关组件
            if (
              id.includes("/modal/") ||
              id.includes("/message/") ||
              id.includes("/notification/") ||
              id.includes("/drawer/") ||
              id.includes("/popconfirm/")
            ) {
              return "antd-feedback"
            }
            // 其他 Ant Design 组件
            return "antd-other"
          }

          // 图标库单独分割
          if (id.includes("@ant-design/icons-vue")) {
            return "antd-icons"
          }

          // 其他第三方库
          if (id.includes("node_modules")) {
            if (id.includes("axios")) return "vendor-axios"
            if (id.includes("dayjs")) return "vendor-dayjs"
            return "vendor-other"
          }

          // 按页面模块分割
          if (id.includes("/views/")) {
            if (id.includes("/views/system/")) return "pages-system"
            if (id.includes("/views/opsmanage/")) return "pages-opsmanage"
            if (id.includes("/views/solutionmanage/")) return "pages-solution"
            return "pages-other"
          }
        },
        // 分割策略：根据文件大小和依赖关系自动分割
        chunkFileNames: chunkInfo => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            // 根据路由分割页面组件
            if (facadeModuleId.includes("/views/")) {
              const pathParts = facadeModuleId.split("/views/")[1].split("/")
              if (pathParts.length > 1) {
                return `js/pages/${pathParts[0]}/[name]-[hash].js`
              }
              return `js/pages/[name]-[hash].js`
            }
            // 组件分割
            if (facadeModuleId.includes("/components/")) {
              return `js/components/[name]-[hash].js`
            }
          }
          return `js/[name]-[hash].js`
        },
        // 入口文件命名
        entryFileNames: "js/[name]-[hash].js",
        // 资源文件命名
        assetFileNames: assetInfo => {
          const info = assetInfo.name.split(".")
          const ext = info[info.length - 1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            return `media/[name]-[hash].${ext}`
          }
          if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
            return `images/[name]-[hash].${ext}`
          }
          if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            return `fonts/[name]-[hash].${ext}`
          }
          return `assets/[name]-[hash].${ext}`
        }
      }
    }
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src")
    }
  },
  server: {
    host: "0.0.0.0", // 允许外部访问
    port: 5180, // 可以自定义端口号
    open: true // 自动打开浏览器
    // proxy: {
    //   "/api": {
    //     target: "http://localhost:8080",
    //     changeOrigin: true,
    //     rewrite: path => path.replace(/^\/api/, "")
    //   }
    // }
  }
})
