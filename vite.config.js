import { defineConfig } from "vite"
import vue from "@vitejs/plugin-vue"
import { resolve } from "path"
import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { AntDesignVueResolver } from "unplugin-vue-components/resolvers"

export default defineConfig({
  plugins: [
    vue(),
    // 自动导入Vue3 API
    AutoImport({
      imports: [
        "vue",
        "vue-router",
        "pinia",
        {
          "ant-design-vue": ["message", "Modal", "notification"]
        }
      ],
      dts: true, // 生成类型声明文件
      eslintrc: {
        enabled: true // 生成eslint配置
      }
    }),
    // 自动导入组件
    Components({
      resolvers: [
        AntDesignVueResolver({
          importStyle: false // css in js
        })
      ],
      dts: true // 生成类型声明文件
    })
  ],
  build: {
    chunkSizeWarningLimit: 300 // 允许最大 chunk 大小（默认 500kb）
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src")
    }
  },
  server: {
    host: "0.0.0.0", // 允许外部访问
    port: 5180, // 可以自定义端口号
    open: true // 自动打开浏览器
    // proxy: {
    //   "/api": {
    //     target: "http://localhost:8080",
    //     changeOrigin: true,
    //     rewrite: path => path.replace(/^\/api/, "")
    //   }
    // }
  }
})
