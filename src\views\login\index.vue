<template>
  <div class="login-container">
    <!-- 粒子效果 -->
    <div class="particles">
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
      <div class="particle"></div>
    </div>

    <div class="login-box">
      <div class="login-header">
        <h1>劝学后台管理系统</h1>
        <!-- <p>欢迎登录</p> -->
      </div>

      <a-form :model="loginForm" :rules="rules" @finish="handleLogin" class="login-form">
        <a-form-item name="username">
          <a-input v-model:value="loginForm.username" size="large" placeholder="请输入用户名">
            <template #prefix>
              <user-outlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item name="password">
          <a-input-password
            v-model:value="loginForm.password"
            size="large"
            placeholder="请输入密码"
          >
            <template #prefix>
              <lock-outlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit" size="large" :loading="loading" block>
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup name="Login">
import { UserOutlined, LockOutlined } from "@ant-design/icons-vue"
import { useUserStore } from "@/stores/user"
import { loginApi, getUserMenuApi } from "@/api/index.js"
import { addDynamicRoutes } from "@/utils/index.js"

const userStore = useUserStore()
const router = useRouter()
const loading = ref(false)
const loginForm = reactive({
  username: "",
  password: ""
})
const rules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }]
}

/**
 * 登录
 * @param {*} values
 */
const handleLogin = async values => {
  loading.value = true
  loginApi({
    account: values.username,
    pwd: values.password
  })
    .then(res => {
      if (res.code === 200) {
        message.success("登录成功")
        localStorage.setItem("token", res.data.token)
        userStore.setUserInfo({
          name: res.data.name
        })
        getUserMenuApi().then(resp => {
          if (resp.code === 200) {
            console.log("用户菜单数据", resp)
            // 路由假数据
            // const testdata = [
            //   // {
            //   //   path: "/",
            //   //   component: "Layout",
            //   //   redirect: "/dashboard",
            //   //   children: [
            //   //     {
            //   //       path: "dashboard",
            //   //       name: "Dashboard",
            //   //       component: () => import("@/views/dashboard/index.vue"),
            //   //       meta: { title: "仪表盘", icon: "DashboardOutlined" }
            //   //     }
            //   //   ]
            //   // },
            //   {
            //     path: "/system",
            //     component: "Layout",
            //     meta: { title: "系统管理", icon: "SettingOutlined" },
            //     children: [
            //       {
            //         // 跳转路径，会跟上级的path拼接形成完整的路由跳转路径
            //         path: "role",
            //         // 组件名称
            //         component: "role",
            //         meta: { title: "角色管理", icon: "TeamOutlined" }
            //       },
            //       {
            //         path: "menu",
            //         component: "menu",
            //         meta: { title: "菜单管理", icon: "MenuOutlined" }
            //       },
            //       {
            //         path: "cloudschool",
            //         component: "cloudschool",
            //         meta: { title: "云校管理", icon: "UserOutlined" }
            //       },
            //       {
            //         path: "tasktype",
            //         component: "tasktype",
            //         meta: { title: "任务类型管理", icon: "LikeOutlined" }
            //       },
            //       {
            //         path: "dic",
            //         component: "dic",
            //         meta: { title: "参数字典管理", icon: "NodeCollapseOutlined" }
            //       },
            //       {
            //         path: "manager",
            //         component: "manager",
            //         meta: { title: "管理员管理", icon: "ReconciliationOutlined" }
            //       },
            //       {
            //         path: "fund",
            //         component: "fund",
            //         meta: { title: "经费管理", icon: "AlipayOutlined" }
            //       },
            //       {
            //         path: "scholl",
            //         component: "scholl",
            //         meta: { title: "学校管理", icon: "PlayCircleOutlined" }
            //       }
            //     ]
            //   }
            // ]

            resp.data.forEach(item => {
              if (item.children && item.children.length > 0) {
                item.children.forEach(i => {
                  i.path = i.path.replace("/", "")
                })
              }
            })
            localStorage.setItem("userMenu", JSON.stringify(resp.data))
            addDynamicRoutes(router, resp.data)
            setTimeout(() => {
              router.push("/")
            }, 500)
          }
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  // 添加几何背景图案
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      // 大圆形
      radial-gradient(
        circle at 15% 25%,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 25%,
        transparent 50%
      ),
      radial-gradient(
        circle at 85% 75%,
        rgba(255, 255, 255, 0.08) 0%,
        rgba(255, 255, 255, 0.03) 30%,
        transparent 60%
      ),
      // 小点阵
      radial-gradient(circle, rgba(255, 255, 255, 0.15) 1px, transparent 1px),
      // 线条图案
      linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%),
      linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%);
    background-size:
      800px 800px,
      600px 600px,
      40px 40px,
      120px 120px,
      120px 120px;
    background-position:
      -200px -200px,
      calc(100% + 100px) calc(100% + 100px),
      0 0,
      0 0,
      0 0;
    animation: backgroundMove 25s ease-in-out infinite;
    pointer-events: none;
  }

  // 浮动几何形状
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      // 三角形
      conic-gradient(
        from 0deg at 20% 30%,
        transparent 0deg,
        rgba(255, 255, 255, 0.1) 120deg,
        transparent 240deg
      ),
      conic-gradient(
        from 180deg at 80% 70%,
        transparent 0deg,
        rgba(255, 255, 255, 0.08) 120deg,
        transparent 240deg
      ),
      // 六边形图案
      radial-gradient(
          ellipse at center,
          transparent 30%,
          rgba(255, 255, 255, 0.05) 31%,
          rgba(255, 255, 255, 0.05) 35%,
          transparent 36%
        );
    background-size:
      300px 300px,
      250px 250px,
      80px 80px;
    background-position:
      -50px -50px,
      calc(100% + 25px) calc(100% + 25px),
      0 0;
    animation: geometryFloat 20s linear infinite;
    pointer-events: none;
  }

  // 添加粒子效果容器
  .particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .particle {
      position: absolute;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      animation: particleFloat 15s linear infinite;

      &:nth-child(1) {
        width: 4px;
        height: 4px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
        animation-duration: 12s;
      }

      &:nth-child(2) {
        width: 6px;
        height: 6px;
        top: 60%;
        left: 20%;
        animation-delay: 2s;
        animation-duration: 18s;
      }

      &:nth-child(3) {
        width: 3px;
        height: 3px;
        top: 40%;
        left: 80%;
        animation-delay: 4s;
        animation-duration: 14s;
      }

      &:nth-child(4) {
        width: 5px;
        height: 5px;
        top: 80%;
        left: 70%;
        animation-delay: 6s;
        animation-duration: 16s;
      }

      &:nth-child(5) {
        width: 2px;
        height: 2px;
        top: 30%;
        left: 60%;
        animation-delay: 8s;
        animation-duration: 20s;
      }

      &:nth-child(6) {
        width: 4px;
        height: 4px;
        top: 70%;
        left: 30%;
        animation-delay: 10s;
        animation-duration: 13s;
      }
    }
  }

  .login-box {
    width: 400px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.12),
      0 4px 16px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.2);
    padding: 40px;
    position: relative;
    z-index: 10;

    // 添加内部装饰元素
    // &::before {
    //   content: '';
    //   position: absolute;
    //   top: -2px;
    //   left: -2px;
    //   right: -2px;
    //   bottom: -2px;
    //   background: linear-gradient(45deg,
    //     rgba(102, 126, 234, 0.3),
    //     rgba(118, 75, 162, 0.3),
    //     rgba(102, 126, 234, 0.3)
    //   );
    //   border-radius: 22px;
    //   z-index: -1;
    //   animation: borderGlow 3s ease-in-out infinite alternate;
    // }

    // &::after {
    //   content: '';
    //   position: absolute;
    //   top: 10px;
    //   right: 10px;
    //   width: 60px;
    //   height: 60px;
    //   background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
    //   border-radius: 50%;
    //   pointer-events: none;
    // }

    .login-header {
      text-align: center;
      margin-bottom: 32px;
      position: relative;

      // 添加装饰性图标
      // &::before {
      //   content: '🏢';
      //   font-size: 32px;
      //   display: block;
      //   margin-bottom: 16px;
      //   animation: iconBounce 2s ease-in-out infinite;
      // }

      h1 {
        color: #333;
        margin-bottom: 8px;
        font-size: 28px;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradientShift 4s ease-in-out infinite;
      }

      p {
        color: #666;
        margin: 0;
        font-size: 16px;
        font-weight: 400;
        opacity: 0.8;
      }
    }

    .login-form {
      .ant-form-item {
        margin-bottom: 24px;
      }

      // 美化复选框
      :deep(.ant-checkbox-wrapper) {
        color: #666;
        font-weight: 500;

        .ant-checkbox {
          .ant-checkbox-inner {
            border-radius: 4px;
            border-color: #d0d0d0;
            transition: all 0.3s ease;
          }

          &.ant-checkbox-checked .ant-checkbox-inner {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-color: #667eea;
          }
        }
      }

      // 美化按钮
      :deep(.ant-btn-primary) {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border: none;
        border-radius: 12px;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover {
          background: linear-gradient(135deg, #5a6fd8, #6a4190);
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(-1px);
        }
      }
    }
  }
}

// 动画效果
@keyframes backgroundMove {
  0%,
  100% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(20px, -20px) rotate(1deg);
  }
  50% {
    transform: translate(-10px, 10px) rotate(-0.5deg);
  }
  75% {
    transform: translate(15px, 5px) rotate(0.5deg);
  }
}

@keyframes geometryFloat {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  25% {
    transform: translate(30px, -30px) rotate(90deg);
  }
  50% {
    transform: translate(-20px, 20px) rotate(180deg);
  }
  75% {
    transform: translate(25px, -10px) rotate(270deg);
  }
  100% {
    transform: translate(0, 0) rotate(360deg);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0px) translateX(0px) scale(1);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(50px) scale(0.5);
    opacity: 0;
  }
}

@keyframes borderGlow {
  0% {
    opacity: 0.5;
    filter: blur(2px);
  }
  100% {
    opacity: 0.8;
    filter: blur(4px);
  }
}

@keyframes iconBounce {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes gradientShift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 20px;

    .login-box {
      width: 100%;
      max-width: 360px;
      padding: 30px 24px;
      border-radius: 16px;

      .login-header {
        &::before {
          font-size: 24px;
          margin-bottom: 12px;
        }

        h1 {
          font-size: 24px;
        }

        p {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
