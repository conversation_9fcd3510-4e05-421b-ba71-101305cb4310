<template>
  <div class="manager-management">
    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd">新增</a-button>
      <a-button @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0">
        删除
      </a-button>
    </div>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :row-selection="rowSelection"
      :pagination="pagination"
      :loading="loading"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'roleEnum'">
          {{ record.roleEnum == 1 ? "超级管理员" : "云校管理员" }}
        </template>

        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" size="small" @click="handleChangePassword(record)">
              更改密码
            </a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增弹窗 -->
    <a-modal
      v-model:open="addModalVisible"
      title="新增"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleAddSubmit"
      @cancel="handleAddCancel"
    >
      <a-form ref="addFormRef" :model="addFormData" :rules="addRules" layout="vertical">
        <a-form-item label="账号" name="account">
          <a-input v-model:value="addFormData.account" placeholder="请输入账号" />
        </a-form-item>

        <a-form-item label="名称" name="name">
          <a-input v-model:value="addFormData.name" placeholder="请输入名称" />
        </a-form-item>

        <a-form-item label="关联云校" name="cloudSchoolId">
          <a-select
            v-model:value="addFormData.cloudSchoolId"
            placeholder="请选择关联云校"
            :options="cloudSchoolOptions"
          />
        </a-form-item>

        <a-form-item label="角色" name="roleEnum">
          <a-select
            v-model:value="addFormData.roleEnum"
            placeholder="请选择角色"
            :options="roleOptions"
          />
        </a-form-item>

        <a-form-item label="密码" name="pwd">
          <a-input-password v-model:value="addFormData.pwd" placeholder="请输入密码" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 编辑弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      title="编辑"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleEditSubmit"
      @cancel="handleEditCancel"
    >
      <a-form ref="editFormRef" :model="editFormData" :rules="editRules" layout="vertical">
        <a-form-item label="账号" name="account">
          <a-input v-model:value="editFormData.account" placeholder="请输入账号" />
        </a-form-item>

        <a-form-item label="名称" name="name">
          <a-input v-model:value="editFormData.name" placeholder="请输入名称" />
        </a-form-item>

        <a-form-item label="关联云校" name="cloudSchoolId">
          <a-select
            v-model:value="editFormData.cloudSchoolId"
            placeholder="请选择关联云校"
            :options="cloudSchoolOptions"
          />
        </a-form-item>

        <a-form-item label="角色" name="roleEnum">
          <a-select
            v-model:value="editFormData.roleEnum"
            placeholder="请选择角色"
            :options="roleOptions"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 更改密码弹窗 -->
    <a-modal
      v-model:open="passwordModalVisible"
      title="更改密码"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handlePasswordSubmit"
      @cancel="handlePasswordCancel"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordFormData"
        :rules="passwordRules"
        layout="vertical"
      >
        <a-form-item label="新密码" name="newPassword">
          <a-input-password
            v-model:value="passwordFormData.newPassword"
            placeholder="请输入新密码"
          />
        </a-form-item>

        <a-form-item label="再次输入" name="confirmPassword">
          <a-input-password
            v-model:value="passwordFormData.confirmPassword"
            placeholder="请再次输入新密码"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="Manager">
import {
  getManagerListApi,
  addManagerApi,
  editManagerApi,
  deleteManagerApi,
  changePasswordApi,
  getManagerDetailApi,
  getCloudSchoolDropdownListApi
} from "@/api/index.js"
import { watch } from "vue"

// 响应式数据
const addModalVisible = ref(false)
const editModalVisible = ref(false)
const passwordModalVisible = ref(false)
const addFormRef = ref()
const editFormRef = ref()
const passwordFormRef = ref()
const selectedRowKeys = ref([])
const loading = ref(false)
const submitLoading = ref(false)
const currentRecord = ref(null)

// 新增表单数据
const addFormData = reactive({
  account: "",
  name: "",
  cloudSchoolId: null,
  roleEnum: null,
  pwd: ""
})

// 编辑表单数据
const editFormData = reactive({
  id: null,
  account: "",
  name: "",
  cloudSchoolId: null,
  roleEnum: null
})

// 更改密码表单数据
const passwordFormData = reactive({
  id: null,
  newPassword: "",
  confirmPassword: ""
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    getManagerList()
  },
  onShowSizeChange: (current, size) => {
    pagination.current = 1
    pagination.pageSize = size
    getManagerList()
  }
})

// 表格列配置
const columns = [
  {
    title: "选择",
    width: 60,
    align: "center"
  },
  {
    title: "id",
    dataIndex: "id",
    key: "id",
    width: 80
  },
  {
    title: "名称",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "账号",
    dataIndex: "account",
    key: "account"
  },
  {
    title: "关联云校",
    dataIndex: "cloudSchoolName",
    key: "cloudSchoolName"
  },
  {
    title: "角色",
    dataIndex: "roleEnum",
    key: "roleEnum"
  },

  {
    title: "操作",
    key: "action",
    width: 250
  }
]

// 表格数据
const dataSource = ref([])

// 行选择配置
const rowSelection = {
  selectedRowKeys,
  onChange: keys => {
    selectedRowKeys.value = keys
  }
}

// 云校选
const cloudSchoolOptions = ref([])
watch(
  () => addModalVisible.value,
  val => {
    if (val) {
      getCloudSchoolDropdownListApi().then(res => {
        if (res.code === 200) {
          cloudSchoolOptions.value = res.data.map(i => {
            return { label: i.name, value: i.id }
          })
        }
      })
    }
  }
)
watch(
  () => editModalVisible.value,
  val => {
    if (val) {
      getCloudSchoolDropdownListApi().then(res => {
        if (res.code === 200) {
          cloudSchoolOptions.value = res.data.map(i => {
            return { label: i.name, value: i.id }
          })
        }
      })
    }
  }
)

// 角色选项
const roleOptions = ref([
  { label: "超级管理员", value: 1 },
  { label: "云校管理员", value: 2 }
])

// 新增表单验证规则
const addRules = {
  account: [
    { required: true, message: "请输入账号", trigger: "blur" },
    { min: 3, max: 20, message: "账号长度为3-20个字符", trigger: "blur" }
  ],
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 2, max: 50, message: "名称长度为2-50个字符", trigger: "blur" }
  ],
  cloudSchoolId: [{ required: true, message: "请选择关联云校", trigger: "change" }],
  roleEnum: [{ required: true, message: "请选择角色", trigger: "change" }],
  pwd: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度为6-20个字符", trigger: "blur" }
  ]
}

// 编辑表单验证规则
const editRules = {
  account: [
    { required: true, message: "请输入账号", trigger: "blur" },
    { min: 3, max: 20, message: "账号长度为3-20个字符", trigger: "blur" }
  ],
  name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 2, max: 50, message: "名称长度为2-50个字符", trigger: "blur" }
  ],
  cloudSchoolId: [{ required: true, message: "请选择关联云校", trigger: "change" }],
  roleEnum: [{ required: true, message: "请选择角色", trigger: "change" }]
}

// 密码验证器
const validateConfirmPassword = (rule, value) => {
  if (value && value !== passwordFormData.newPassword) {
    return Promise.reject(new Error("两次输入的密码不一致"))
  }
  return Promise.resolve()
}

// 更改密码表单验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, max: 20, message: "密码长度为6-20个字符", trigger: "blur" }
  ],
  confirmPassword: [
    { required: true, message: "请再次输入新密码", trigger: "blur" },
    { validator: validateConfirmPassword, trigger: "blur" }
  ]
}

/**
 * 获取管理员列表数据
 */
const getManagerList = async () => {
  if (loading.value) {
    return
  }

  try {
    loading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize
    }

    const res = await getManagerListApi(params)
    console.log("管理员列表数据:", res)

    if (res.code === 200) {
      dataSource.value = res.data.items || []
      pagination.total = res.data.total || 0
    } else {
      message.error(res.msg || "获取数据失败")
    }
  } catch (error) {
    console.error("获取管理员列表失败:", error)
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  addModalVisible.value = true
  Object.assign(addFormData, {
    account: "",
    name: "",
    cloudSchoolId: null,
    roleEnum: null,
    pwd: ""
  })
}

// 编辑
const handleEdit = async record => {
  editModalVisible.value = true
  currentRecord.value = record

  try {
    // 获取管理员详情
    const res = await getManagerDetailApi(record.id)
    if (res.code === 200) {
      Object.assign(editFormData, {
        id: res.data.id,
        account: res.data.account,
        name: res.data.name,
        cloudSchoolId: res.data.cloudSchoolId,
        roleEnum: res.data.roleEnum
      })
    } else {
      // 如果获取详情失败，使用传入的record数据
      Object.assign(editFormData, {
        id: record.id,
        account: record.account,
        name: record.name,
        cloudSchoolId: record.cloudSchoolId,
        roleEnum: record.roleEnum
      })
    }
  } catch (error) {
    console.error("获取管理员详情失败:", error)
    // 使用传入的record数据作为备选
    Object.assign(editFormData, {
      id: record.id,
      account: record.account,
      name: record.name,
      cloudSchoolId: record.cloudSchoolId,
      roleEnum: record.roleEnum
    })
  }
}

// 更改密码
const handleChangePassword = record => {
  passwordModalVisible.value = true
  currentRecord.value = record
  Object.assign(passwordFormData, {
    id: record.id,
    newPassword: "",
    confirmPassword: ""
  })
}

// 删除单条记录
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除管理员 "${record.name}" 吗？`,
    async onOk() {
      try {
        const res = await deleteManagerApi([record.id])
        if (res.code === 200) {
          message.success("删除成功")
          getManagerList()
        } else {
          message.error(res.msg || "删除失败")
        }
      } catch (error) {
        console.error("删除失败:", error)
      }
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    async onOk() {
      try {
        const res = await deleteManagerApi(selectedRowKeys.value)
        if (res.code === 200) {
          message.success("批量删除成功")
          selectedRowKeys.value = []
          getManagerList()
        } else {
          message.error(res.msg || "批量删除失败")
        }
      } catch (error) {
        console.error("批量删除失败:", error)
        // message.error("批量删除失败")
      }
    }
  })
}

// 新增提交
const handleAddSubmit = async () => {
  try {
    await addFormRef.value.validate()
    submitLoading.value = true
    // console.log("addFormData", addFormData)
    // return
    const res = await addManagerApi(addFormData)
    if (res.code === 200) {
      message.success("新增成功")
      addModalVisible.value = false
      getManagerList()
    } else {
      message.error(res.msg || "新增失败")
    }
  } catch (error) {
    console.error("新增失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 编辑提交
const handleEditSubmit = async () => {
  try {
    await editFormRef.value.validate()
    submitLoading.value = true
    const res = await editManagerApi(editFormData)
    if (res.code === 200) {
      message.success("更新成功")
      editModalVisible.value = false
      getManagerList()
    } else {
      message.error(res.msg || "更新失败")
    }
  } catch (error) {
    console.error("更新失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 更改密码提交
const handlePasswordSubmit = async () => {
  try {
    await passwordFormRef.value.validate()
    submitLoading.value = true

    const params = {
      id: passwordFormData.id,
      newPassword: passwordFormData.newPassword
    }

    const res = await changePasswordApi(params)
    if (res.code === 200) {
      message.success("密码更改成功")
      passwordModalVisible.value = false
    } else {
      message.error(res.msg || "密码更改失败")
    }
  } catch (error) {
    console.error("密码更改失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const handleAddCancel = () => {
  addModalVisible.value = false
  submitLoading.value = false
}

const handleEditCancel = () => {
  editModalVisible.value = false
  submitLoading.value = false
}

const handlePasswordCancel = () => {
  passwordModalVisible.value = false
  submitLoading.value = false
}

// 组件挂载时获取数据
onMounted(() => {
  getManagerList()
})
</script>

<style lang="scss" scoped>
.manager-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}
</style>
