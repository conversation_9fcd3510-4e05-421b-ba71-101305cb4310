# 劝学后台管理系统

基于 Vue3 + Ant Design Vue 的现代化后台管理系统

## 技术栈

- **前端框架**: Vue 3.3.4
- **UI组件库**: Ant Design Vue 4.0.0
- **构建工具**: Vite 4.4.9
- **路由管理**: Vue Router 4.2.4
- **状态管理**: Pinia 2.1.6
- **HTTP客户端**: Axios 1.5.0
- **样式预处理**: Sass 1.66.1
- **日期处理**: Day.js 1.11.9

## 功能特性

- ✅ 用户登录/退出
- ✅ 动态路由菜单
- ✅ 权限控制
- ✅ 响应式布局
- ✅ 用户管理
- ✅ 角色管理
- ✅ 菜单管理
- ✅ 数据统计面板

## 项目结构

```
oa-bms/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   ├── layout/            # 布局组件
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   ├── style/             # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   │   ├── dashboard/     # 仪表盘
│   │   ├── login/         # 登录页
│   │   └── system/        # 系统管理
│   │       ├── user/      # 用户管理
│   │       ├── role/      # 角色管理
│   │       └── menu/      # 菜单管理
│   ├── App.vue           # 根组件
│   └── main.js           # 入口文件
├── index.html            # HTML模板
├── package.json          # 项目配置
├── vite.config.js        # Vite配置
└── README.md            # 项目说明
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 默认账号

- 用户名: `admin`
- 密码: `123456`

## 开发说明

### 路由配置

路由配置位于 `src/router/index.js`，支持嵌套路由和动态路由。

### 状态管理

使用 Pinia 进行状态管理，store 文件位于 `src/stores/` 目录。

### API 请求

HTTP 请求统一使用 `src/utils/request.js` 中封装的 axios 实例。

### 样式规范

- 使用 SCSS 预处理器
- 全局样式位于 `src/style/index.scss`
- 组件样式使用 scoped 作用域

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License 

<!-- 参数字典管理模块的 排序字段未联调 -->