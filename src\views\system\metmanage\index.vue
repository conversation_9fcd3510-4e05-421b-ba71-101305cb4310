<!-- 德育主题管理 -->
<template>
  <div class="metmanage-management">
    <!-- 操作按钮区域 -->
    <a-card style="margin-bottom: 16px">
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </div>
    </a-card>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :pagination="pagination"
      row-key="id"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleDetail(record)">详情</a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-popconfirm
              title="确定删除吗?"
              ok-text="是"
              cancel-text="否"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" danger>删除</a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="学年" name="semester">
          <a-select v-model:value="formData.semester" placeholder="请选择学年">
            <a-select-option
              v-for="year in schoolYearOptions"
              :key="year.value"
              :value="year.value"
            >
              {{ year.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="阶段" name="upDown">
          <a-select v-model:value="formData.upDown" placeholder="请选择阶段">
            <a-select-option v-for="stage in stageOptions" :key="stage.value" :value="stage.value">
              {{ stage.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="月份" name="semesterMonth">
          <a-select v-model:value="formData.semesterMonth" placeholder="请选择月份">
            <a-select-option v-for="month in monthOptions" :key="month.value" :value="month.value">
              {{ month.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="德育主题" name="semesterTitle">
          <a-input v-model:value="formData.semesterTitle" placeholder="请输入德育主题" />
        </a-form-item>

        <a-form-item label="工作目标" name="classesThemeObjectiveList">
          <div
            v-for="(goal, index) in formData.classesThemeObjectiveList"
            :key="index"
            class="work-goal-item"
          >
            <div style="display: flex; align-items: center; margin-bottom: 8px">
              <a-input
                v-model:value="goal.objectivesTitle"
                :placeholder="`请输入工作目标${index + 1}`"
                style="flex: 1; margin-right: 8px"
              />
              <a-button
                type="text"
                danger
                size="small"
                @click="removeWorkGoal(index)"
                :disabled="formData.classesThemeObjectiveList.length <= 1"
              >
                删除
              </a-button>
            </div>
          </div>
          <a-button type="dashed" block @click="addWorkGoal" style="margin-top: 8px">
            <plus-outlined /> 添加工作目标
          </a-button>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="德育主题详情"
      width="600px"
      :footer="null"
      @cancel="handleDetailCancel"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="学年">
          {{ detailData.semester || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="阶段">
          {{ detailData.upDown || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="月份">
          {{ detailData.semesterMonth || "-" }}月
        </a-descriptions-item>
        <a-descriptions-item label="德育主题">
          {{ detailData.semesterTitle || "-" }}
        </a-descriptions-item>
        <a-descriptions-item label="工作目标">
          <div
            v-if="
              detailData.classesThemeObjectiveList &&
              detailData.classesThemeObjectiveList.length > 0
            "
          >
            <div
              v-for="(goal, index) in detailData.classesThemeObjectiveList"
              :key="index"
              style="margin-bottom: 8px"
            >
              <a-tag color="blue">{{ goal.objectivesTitle }}</a-tag>
            </div>
          </div>
          <div v-else>-</div>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup name="Metmanage">
import { ref, reactive, onMounted, nextTick } from "vue"
import { message } from "ant-design-vue"
import { PlusOutlined } from "@ant-design/icons-vue"
import {
  getThemeListApi,
  addThemeApi,
  deleteThemeApi,
  editThemeApi,
  getThemeDetailApi
} from "@/api"

// 表格列配置
const columns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id"
  },
  {
    title: "学年",
    dataIndex: "semester",
    key: "semester"
  },
  {
    title: "阶段",
    dataIndex: "upDown",
    key: "upDown"
  },
  {
    title: "月份",
    dataIndex: "semesterMonth",
    key: "semesterMonth",

    customRender: ({ text }) => `${text}月`
  },
  {
    title: "德育主题",
    dataIndex: "semesterTitle",
    key: "semesterTitle",
    ellipsis: true
  },
  {
    title: "操作",
    key: "action",
    width: 200,
    fixed: "right"
  }
]

// 响应式数据
const loading = ref(false)
const tableData = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    getTableData()
  },
  onShowSizeChange: (_, size) => {
    pagination.current = 1
    pagination.pageSize = size
    getTableData()
  }
})

// 选项数据
const schoolYearOptions = ref([
  { label: "高一", value: "高一" },
  { label: "高二", value: "高二" },
  { label: "高三", value: "高三" }
])

const stageOptions = ref([
  { label: "上", value: "上" },
  { label: "下", value: "下" }
])

const monthOptions = ref([
  { label: "1", value: 1 },
  { label: "2", value: 2 },
  { label: "3", value: 3 },
  { label: "4", value: 4 },
  { label: "5", value: 5 },
  { label: "6", value: 6 },
  { label: "7", value: 7 },
  { label: "8", value: 8 },
  { label: "9", value: 9 },
  { label: "10", value: 10 },
  { label: "11", value: 11 },
  { label: "12", value: 12 }
])

/**
 * 获取表格数据
 */
const getTableData = async () => {
  loading.value = true

  try {
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize
    }
    const response = await getThemeListApi(params)
    if (response.code === 200) {
      tableData.value = response.data.items || []
      pagination.total = response.data.total || 0
    } else {
      message.error(response.msg || "数据加载失败")
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 弹窗相关
const modalVisible = ref(false)
const modalTitle = ref("新增德育主题")
const submitLoading = ref(false)
const formRef = ref(null)

// 表单数据
const formData = ref({
  id: null,
  semester: "",
  upDown: "",
  semesterMonth: null,
  semesterTitle: "",
  classesThemeObjectiveList: [{ objectivesTitle: "" }]
})

// 表单验证规则
const rules = {
  semester: [{ required: true, message: "请选择学年", trigger: "change" }],
  upDown: [{ required: true, message: "请选择阶段", trigger: "change" }],
  semesterMonth: [{ required: true, message: "请选择月份", trigger: "change" }],
  semesterTitle: [{ required: true, message: "请输入德育主题", trigger: "blur" }],
  classesThemeObjectiveList: [
    {
      validator: (_, value) => {
        if (!value || value.length === 0) {
          return Promise.reject("请至少添加一个工作目标")
        }
        for (let i = 0; i < value.length; i++) {
          if (!value[i].objectivesTitle || value[i].objectivesTitle.trim() === "") {
            return Promise.reject(`请填写工作目标${i + 1}`)
          }
        }
        return Promise.resolve()
      },
      trigger: "blur"
    }
  ]
}

// 详情弹窗相关
const detailVisible = ref(false)
const detailData = ref({})

/**
 * 新增
 */
const handleAdd = () => {
  modalTitle.value = "新增德育主题"
  modalVisible.value = true
  formData.value = {
    id: null,
    semester: null,
    upDown: null,
    semesterMonth: null,
    semesterTitle: "",
    classesThemeObjectiveList: [{ objectivesTitle: "" }]
  }

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

/**
 * 编辑
 */
const handleEdit = async record => {
  try {
    modalTitle.value = "编辑德育主题"
    modalVisible.value = true
    submitLoading.value = true

    console.log("获取德育主题详情:", record.id)

    // 调用详情接口获取完整数据
    const response = await getThemeDetailApi(record.id)
    console.log("详情响应:", response)

    if (response.code === 200) {
      const detailData = response.data
      formData.value = {
        id: detailData.id,
        semester: detailData.semester || "",
        upDown: detailData.upDown || "",
        semesterMonth: detailData.semesterMonth || null,
        semesterTitle: detailData.semesterTitle || "",
        classesThemeObjectiveList: detailData.classesThemeObjectiveList || [{ objectivesTitle: "" }]
      }

      nextTick(() => {
        formRef.value?.clearValidate()
      })
    } else {
      message.error(response.msg || "获取详情失败")
      modalVisible.value = false
    }
  } catch (error) {
    console.error("获取详情失败:", error)
    message.error("获取详情失败，请检查网络连接")
    modalVisible.value = false
  } finally {
    submitLoading.value = false
  }
}

/**
 * 详情
 */
const handleDetail = async record => {
  try {
    console.log("查看德育主题详情:", record.id)

    const response = await getThemeDetailApi(record.id)
    console.log("详情响应:", response)

    if (response.code === 200) {
      detailData.value = response.data
      detailVisible.value = true
    } else {
      message.error(response.msg || "获取详情失败")
    }
  } catch (error) {
    console.error("获取详情失败:", error)
    message.error("获取详情失败，请检查网络连接")
  }
}

/**
 * 删除
 */
const handleDelete = async record => {
  try {
    console.log("删除德育主题:", record)

    const response = await deleteThemeApi([record.id])
    console.log("删除响应:", response)

    if (response.code === 200) {
      message.success("删除成功")
      // 如果当前页没有数据了，回到上一页
      if (tableData.value.length === 1 && pagination.current > 1) {
        pagination.current = pagination.current - 1
      }
      getTableData()
    } else {
      message.error(response.msg || "删除失败")
    }
  } catch (error) {
    console.error("删除失败:", error)
    message.error("删除失败，请检查网络连接")
  }
}

/**
 * 提交表单
 */
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    console.log("提交表单数据:", formData.value)

    // 准备提交的数据
    const submitData = {
      ...formData.value
    }

    // 如果是编辑，移除id字段（根据接口需求调整）
    if (formData.value.id) {
      // 编辑
      console.log("编辑德育主题:", submitData)

      const response = await editThemeApi(submitData)
      console.log("编辑响应:", response)

      if (response.code === 200) {
        message.success("编辑成功")
        modalVisible.value = false
        getTableData()
      } else {
        message.error(response.msg || "编辑失败")
      }
    } else {
      // 新增
      delete submitData.id // 新增时移除id字段
      console.log("新增德育主题:", submitData)

      const response = await addThemeApi(submitData)
      console.log("新增响应:", response)

      if (response.code === 200) {
        message.success("新增成功")
        modalVisible.value = false
        getTableData()
      } else {
        message.error(response.msg || "新增失败")
      }
    }
  } catch (error) {
    console.error("提交失败:", error)
  } finally {
    submitLoading.value = false
  }
}

/**
 * 取消弹窗
 */
const handleCancel = () => {
  modalVisible.value = false
  formData.value = {
    id: null,
    semester: "",
    upDown: "",
    semesterMonth: null,
    semesterTitle: "",
    classesThemeObjectiveList: [{ objectivesTitle: "" }]
  }

  nextTick(() => {
    formRef.value?.resetFields()
  })
}

/**
 * 详情弹窗取消
 */
const handleDetailCancel = () => {
  detailVisible.value = false
  detailData.value = {}
}

/**
 * 添加工作目标
 */
const addWorkGoal = () => {
  formData.value.classesThemeObjectiveList.push({ objectivesTitle: "" })
}

/**
 * 删除工作目标
 */
const removeWorkGoal = index => {
  if (formData.value.classesThemeObjectiveList.length > 1) {
    formData.value.classesThemeObjectiveList.splice(index, 1)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  console.log("德育主题管理组件已挂载，开始获取数据...")
  getTableData()
})
</script>

<style scoped lang="scss">
.metmanage-management {
  background: #fff;
  border-radius: 8px;

  .table-operations {
    .ant-btn {
      margin-right: 8px;
    }
  }

  // 表格样式优化
  :deep(.ant-table) {
    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 600;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #e6f7ff;
    }
  }

  // 工作目标项样式
  .work-goal-item {
    .ant-input {
      border-radius: 6px;
    }
  }

  // 弹窗表单样式
  :deep(.ant-modal-body) {
    .ant-form-item-label {
      font-weight: 600;
    }
  }

  // 详情页面标签样式
  :deep(.ant-descriptions-item-content) {
    .ant-tag {
      margin-bottom: 4px;
      border-radius: 4px;
    }
  }
}
</style>
