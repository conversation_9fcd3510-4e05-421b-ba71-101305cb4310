<template>
  <div class="cloud-school-management">
    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd"> 新增 </a-button>
      <a-button @click="handleBatchDelete" :disabled="selectedRowKeys.length === 0">
        删除
      </a-button>
    </div>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :row-selection="rowSelection"
      :pagination="pagination"
      :loading="loading"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'enable'">
          <a-badge
            :status="record.enable ? 'success' : 'error'"
            :text="record.enable ? '启用' : '禁用'"
          />
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="getDetail(record)"> 详情 </a-button>
            <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="云校名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入云校名称" />
        </a-form-item>

        <a-form-item label="启用状态" name="enable">
          <a-radio-group v-model:value="formData.enable">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="云校详情"
      width="600px"
      :footer="null"
      @cancel="handleDetailCancel"
    >
      <div class="detail-content">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="云校名称">
            {{ detailData.name || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="学习官部长">
            {{ detailData.ministerName || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="关联学校">
            <div v-if="detailData.schools && detailData.schools.length > 0">
              <a-tag
                v-for="school in detailData.schools"
                :key="school.id"
                style="margin-bottom: 4px; margin-right: 4px"
              >
                {{ school.name }}
              </a-tag>
            </div>
            <span v-else>-</span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup name="CloudSchool">
import {
  addCloudSchoolApi,
  editCloudSchoolApi,
  deleteCloudSchoolApi,
  getCloudSchoolListApi,
  getCloudSchoolDetailApi
} from "@/api/index.js"

// 响应式数据
const modalVisible = ref(false)
const detailVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const selectedRowKeys = ref([])
const loading = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  name: "",
  enable: true
})

// 详情数据
const detailData = reactive({
  name: "",
  ministerName: "",
  schools: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    console.log("分页变化:", { page, pageSize })
    pagination.current = page
    pagination.pageSize = pageSize
    getCloudSchoolList()
  },
  onShowSizeChange: (current, size) => {
    console.log("页面大小变化:", { current, size })
    pagination.current = 1
    pagination.pageSize = size
    getCloudSchoolList()
  }
})

// 表格列配置
const columns = [
  {
    title: "选择",
    width: 60,
    align: "center"
  },
  {
    title: "id",
    dataIndex: "id",
    key: "id",
    width: 80
  },
  {
    title: "云校名称",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "启用状态",
    dataIndex: "enable",
    key: "enable"
  },
  {
    title: "操作",
    key: "action",
    width: 250
  }
]

// 表格数据
const dataSource = ref([])

// 行选择配置
const rowSelection = {
  selectedRowKeys,
  onChange: keys => {
    selectedRowKeys.value = keys
  }
}

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入云校名称", trigger: "blur" }],
  enable: [{ required: true, message: "请选择启用状态", trigger: "change" }]
}

/**
 * 获取云校列表数据
 */
const getCloudSchoolList = async () => {
  // 如果正在加载中，直接返回，避免重复调用
  if (loading.value) {
    console.log("正在加载中，跳过重复调用")
    return
  }

  try {
    loading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize
    }

    console.log("调用列表接口，参数:", params)
    const res = await getCloudSchoolListApi(params)
    console.log("云校列表数据:", res)

    if (res.code === 200) {
      dataSource.value = res.data.items || []
      pagination.total = res.data.total || 0
    } else {
      message.error(res.msg || "获取数据失败")
    }
  } catch (error) {
    console.error("获取云校列表失败:", error)
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  modalTitle.value = "新增"
  modalVisible.value = true
  Object.assign(formData, {
    id: null,
    name: "",
    enable: true
  })
}

// 查询详情
const getDetail = async record => {
  try {
    console.log("查询云校详情", record.id)
    const res = await getCloudSchoolDetailApi(record.id)
    console.log("云校详情数据:", res)

    if (res.code === 200) {
      const data = res.data
      Object.assign(detailData, {
        name: data.name || "",
        ministerName: data.ministerName || "",
        schools: data.schools || []
      })
      detailVisible.value = true
    } else {
      message.error(res.msg || "获取详情失败")
    }
  } catch (error) {
    console.error("获取云校详情失败:", error)
    // message.error("获取详情失败")
  }
}

// 编辑
const handleEdit = async record => {
  modalTitle.value = "编辑"
  modalVisible.value = true

  try {
    const res = await getCloudSchoolDetailApi(record.id)
    console.log("编辑-云校详情", res)

    if (res.code === 200) {
      const data = res.data
      Object.assign(formData, {
        id: data.id,
        name: data.name,
        enable: data.enable
      })
    } else {
      // 如果获取详情失败，使用表格数据
      Object.assign(formData, {
        id: record.id,
        name: record.name,
        enable: record.enable
      })
      message.warning("获取详情失败，使用当前数据")
    }
  } catch (error) {
    console.error("获取云校详情失败:", error)
    // 使用表格数据作为备选
    Object.assign(formData, {
      id: record.id,
      name: record.name,
      enable: record.enable
    })
    // message.error("获取详情失败")
  }
}

// 删除单条记录
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除 "${record.name}" 吗？`,
    async onOk() {
      const res = await deleteCloudSchoolApi([record.id])
      if (res.code === 200) {
        message.success("删除成功")
        getCloudSchoolList()
      } else {
        message.error(res.msg || "删除失败")
      }
    }
  })
}

// 批量删除
const handleBatchDelete = () => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
    async onOk() {
      const res = await deleteCloudSchoolApi(selectedRowKeys.value)
      if (res.code === 200) {
        message.success("批量删除成功")
        selectedRowKeys.value = []
        getCloudSchoolList()
      } else {
        message.error(res.msg || "批量删除失败")
      }
    }
  })
}
const submitLoading = ref(false)
// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    if (formData.id) {
      // 编辑
      const res = await editCloudSchoolApi(formData)
      if (res.code === 200) {
        message.success("更新成功")
        modalVisible.value = false
        getCloudSchoolList()
      } else {
        message.error(res.msg || "更新失败")
      }
    } else {
      // 新增
      const params = { ...formData }
      delete params.id

      const res = await addCloudSchoolApi(params)
      if (res.code === 200) {
        message.success("新增成功")
        modalVisible.value = false
        getCloudSchoolList()
      } else {
        message.error(res.msg || "新增失败")
      }
    }
  } catch (error) {
    console.log("表单验证失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
}

const handleDetailCancel = () => {
  detailVisible.value = false
}

onMounted(() => {
  // 初始化加载数据
  getCloudSchoolList()
})
</script>

<style lang="scss" scoped>
.cloud-school-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}

.detail-content {
  .ant-descriptions-item-label {
    font-weight: 500;
  }
}
</style>
