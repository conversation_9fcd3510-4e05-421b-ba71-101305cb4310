<!-- 班级列表 -->
<template>
  <div class="grade-management-cp">
    <!-- 搜索区域 -->
    <div class="search-area">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="班级名称">
          <a-input
            v-model:value="searchForm.className"
            placeholder="请输入班级名称"
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item label="班级类型">
          <a-select
            v-model:value="searchForm.teachingLevel"
            placeholder="请选择班级类型"
            style="width: 150px"
            allow-clear
          >
            <a-select-option :value="1">普通本科</a-select-option>
            <a-select-option :value="2">重点本科</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">查询</a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd"> 添加班级 </a-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-wrapper">
      <!-- 空状态 -->
      <!-- <div v-if="localClassData.length === 0 && !loading" class="empty-state">
        <div class="empty-icon">📚</div>
        <div class="empty-text">暂无班级数据</div>
        <div class="empty-desc">
          <span v-if="!props.searchParams.SchoolId">请先在左侧选择学校和年级</span>
          <span v-else>当前年级下暂无班级，点击"添加班级"开始创建</span>
        </div>
        <a-button
          v-if="props.searchParams.SchoolId"
          type="primary"
          @click="handleAdd"
          style="margin-top: 16px"
        >
          添加班级
        </a-button>
      </div> -->

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="localClassData"
        :pagination="false"
        row-key="id"
        :loading="loading"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'teachingLevel'">
            <a-tag :color="record.teachingLevel === 1 ? 'blue' : 'red'">
              {{ getTeachingLevelText(record.teachingLevel) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)"> 修改 </a-button>
              <a-popconfirm
                title="确定删除吗?"
                ok-text="是"
                cancel-text="否"
                @confirm="handleDelete(record)"
              >
                <a href="#" style="color: #ff4d4f">删除</a>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 添加/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="学校" name="schoolId">
          <a-select
            v-model:value="formData.schoolId"
            placeholder="请选择学校"
            style="width: 100%"
            show-search
            :disabled="modalTitle === '编辑班级'"
            :filter-option="filterOption"
          >
            <a-select-option
              v-for="school in schoolOptions"
              :key="school.value"
              :value="school.value"
              :label="school.label"
            >
              {{ school.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="年级" name="grade">
          <a-select v-model:value="formData.grade" placeholder="请选择年级" style="width: 100%">
            <a-select-option
              v-for="grade in gradeOptions"
              :key="grade.value"
              :value="grade.value"
              :label="grade.label"
            >
              {{ grade.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="班级名称" name="className">
          <a-input
            v-model:value="formData.className"
            placeholder="请输入班级名称"
            :maxlength="50"
          />
        </a-form-item>

        <a-form-item label="班级类型" name="teachingLevel">
          <a-select
            v-model:value="formData.teachingLevel"
            placeholder="请选择班级类型"
            style="width: 100%"
          >
            <a-select-option :value="1">普通本科</a-select-option>
            <a-select-option :value="2">重点本科</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="GradeManageCp">
import { reactive, ref, watch } from "vue"
import { message } from "ant-design-vue"
import { PlusOutlined } from "@ant-design/icons-vue"
import {
  getSchoolDropdownListApi,
  getGradeListApi,
  getClassListApi,
  addClassListApi,
  deleteClassListApi,
  getTeacherListApi,
  getStudentsListApi,
  getLearnOfficeApi,
  getGradeDataApi,
  editClassListApi
} from "@/api/index.js"

const props = defineProps({
  classDataProps: {
    type: Array,
    default: () => []
  },
  searchParams: {
    type: Object,
    default: () => {}
  }
})

// 定义emit事件
const emit = defineEmits(["update:classDataProps", "updateTree"])

// 本地数据源，用于搜索后的结果显示
const localClassData = ref([])

// 监听props变化，同步到本地数据
watch(
  () => props.classDataProps,
  newVal => {
    localClassData.value = [...newVal]
  },
  { immediate: true }
)

// 加载状态
const loading = ref(false)

// 弹窗相关状态
const modalVisible = ref(false)
const modalTitle = ref("")
const submitLoading = ref(false)
const formRef = ref()
const currentRecord = ref(null)

watch(
  () => modalVisible.value,
  val => {
    if (val) {
      // 获取学校
      getSchoolDropdownListApi({
        PageIndex: 1,
        PageSize: 9999
      }).then(res => {
        if (res.code === 200) {
          schoolOptions.value = res.data.items.map(item => {
            return {
              label: item.name,
              value: item.id
            }
          })
        }
      })
      // 获取年级
      getGradeDataApi().then(res => {
        if (res.code === 200) {
          gradeOptions.value = res.data.map(item => {
            return {
              label: item.text,
              value: item.text
            }
          })
        }
      })
    }
  }
)
// 表单数据
const formData = reactive({
  id: null,
  schoolId: null,
  grade: null,
  className: "",
  teachingLevel: null
})

// 假数据 - 学校选项
const schoolOptions = ref([
  { value: 1, label: "涪陵十七中学" },
  { value: 2, label: "涪陵第一中学" },
  { value: 3, label: "涪陵第二中学" },
  { value: 4, label: "涪陵第三中学" },
  { value: 5, label: "涪陵实验中学" }
])

// 假数据 - 年级选项
const gradeOptions = ref([
  { value: 1, label: "初一年级" },
  { value: 2, label: "初二年级" },
  { value: 3, label: "初三年级" },
  { value: 4, label: "高一年级" },
  { value: 5, label: "高二年级" },
  { value: 6, label: "高三年级" }
])

// 表单验证规则
const rules = {
  schoolId: [{ required: true, message: "请选择学校", trigger: "change" }],
  grade: [{ required: true, message: "请选择年级", trigger: "change" }],
  className: [
    { required: true, message: "请输入班级名称", trigger: "blur" },
    { min: 1, max: 50, message: "班级名称长度为1-50个字符", trigger: "blur" }
  ],
  teachingLevel: [{ required: true, message: "请选择班级类型", trigger: "change" }]
}

// 学校搜索过滤函数
const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 搜索表单
const searchForm = reactive({
  className: "",
  teachingLevel: undefined
})

// 表格列配置
const columns = [
  {
    title: "班级名称",
    dataIndex: "className",
    key: "className",
    width: 120
  },
  {
    title: "所属学校",
    dataIndex: "schoolName",
    key: "schoolName",
    width: 150
  },
  {
    title: "关联学习官",
    dataIndex: "learningOfficeName",
    key: "learningOfficeName",
    width: 120,
    customRender: ({ text }) => text || "-"
  },
  {
    title: "关联学习官组长",
    dataIndex: "learningOfficeLeaderName",
    key: "learningOfficeLeaderName",
    width: 140,
    customRender: ({ text }) => text || "-"
  },
  {
    title: "班级类型",
    dataIndex: "teachingLevel",
    key: "teachingLevel",
    width: 100
  },
  {
    title: "操作",
    key: "action",
    width: 120,
    fixed: "right"
  }
]

// 获取班级类型文本
const getTeachingLevelText = level => {
  switch (level) {
    case 1:
      return "普通本科"
    case 2:
      return "重点本科"
    default:
      return "未知"
  }
}

// 添加班级事件
const handleAdd = () => {
  console.log("添加班级")
  modalTitle.value = "添加班级"
  modalVisible.value = true
  currentRecord.value = null
  // 重置表单数据
  Object.assign(formData, {
    id: null,
    schoolId: null,
    grade: null,
    className: "",
    teachingLevel: null
  })
}

// 搜索事件
const handleSearch = () => {
  console.log("搜索参数:", searchForm)
  loading.value = true
  // TODO: 实现搜索逻辑
  getClassListApi({
    ...props.searchParams,
    ...searchForm,
    PageIndex: 1,
    PageSize: 9999
  })
    .then(res => {
      if (res.code === 200) {
        console.log("res", res)
        // 更新本地数据而不是直接修改props
        localClassData.value = res.data.items
        // 如果需要同步到父组件，可以使用emit
        // emit("update:classDataProps", res.data.items)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 重置事件
const handleReset = () => {
  searchForm.className = ""
  searchForm.teachingLevel = undefined
  console.log("重置搜索条件")
  // TODO: 实现重置后的数据刷新逻辑
  loading.value = true
  // TODO: 实现搜索逻辑
  getClassListApi({
    ...props.searchParams,
    PageIndex: 1,
    PageSize: 9999
  })
    .then(res => {
      if (res.code === 200) {
        console.log("res", res)
        // 更新本地数据而不是直接修改props
        localClassData.value = res.data.items
        // 如果需要同步到父组件，可以使用emit
        // emit("update:classDataProps", res.data.items)
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 修改事件
const handleEdit = record => {
  console.log("修改班级:", record)
  modalTitle.value = "编辑班级"
  modalVisible.value = true
  currentRecord.value = record
  // 填充表单数据
  Object.assign(formData, {
    id: record.id,
    schoolId: record.schoolId || 1, // 假设数据
    grade: record.gradeString || 1, // 假设数据
    className: record.name || record.className,
    teachingLevel: record.teachingLevel
  })
}

// 删除事件
const handleDelete = record => {
  console.log("删除班级:", record)
  deleteClassListApi([record.id])
    .then(res => {
      if (res.code === 200) {
        message.success("删除成功")

        // 立即从本地数据中移除被删除的记录
        localClassData.value = localClassData.value.filter(item => item.id !== record.id)

        // 触发父组件更新树形数据
        emit("updateTree")
        // 父组件会更新数据，不需要再次调用handleReset
      }
    })
    .catch(error => {
      console.error("删除失败:", error)
      // message.error("删除失败")
    })
}

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    const reqdata = { ...formData }

    // 如果是新增，删除id字段
    if (!reqdata.id) {
      delete reqdata.id
    }

    // 根据是否有id判断是新增还是修改
    const isEdit = !!currentRecord.value
    const successMessage = isEdit ? "修改成功" : "添加成功"

    if (!isEdit) {
      addClassListApi(reqdata)
        .then(res => {
          if (res.code === 200) {
            message.success(successMessage)
            modalVisible.value = false
            // 触发父组件更新树形数据
            emit("updateTree")
            // 父组件会更新数据，不需要再次调用handleReset
          } else {
            message.error(res.message || "操作失败")
          }
        })
        .catch(error => {
          console.error("操作失败:", error)
          // message.error("操作失败")
        })
        .finally(() => {
          submitLoading.value = false
        })
    } else {
      console.log("修改")
      console.log(reqdata)
      const reqData = {
        id: reqdata.id,
        className: reqdata.className,
        teachingLevel: reqdata.teachingLevel,
        grade: formData.grade
      }
      editClassListApi(reqData)
        .then(res => {
          if (res.code === 200) {
            message.success(successMessage)
            modalVisible.value = false

            // 检查是否修改了年级
            const originalRecord = currentRecord.value
            const isGradeChanged = originalRecord && originalRecord.gradeString !== formData.grade

            if (isGradeChanged) {
              // 如果修改了年级，立即从本地数据中移除该记录
              localClassData.value = localClassData.value.filter(item => item.id !== reqData.id)
            } else {
              // 如果没有修改年级，更新本地数据中的记录
              const index = localClassData.value.findIndex(item => item.id === reqData.id)
              if (index !== -1) {
                localClassData.value[index] = {
                  ...localClassData.value[index],
                  className: reqData.className,
                  teachingLevel: reqData.teachingLevel
                }
              }
            }

            // 触发父组件更新树形数据
            emit("updateTree")
            // 父组件会更新数据，不需要再次调用handleReset
          } else {
            message.error(res.message || "操作失败")
          }
        })
        .finally(() => {
          submitLoading.value = false
        })
    }
  } catch (error) {
    console.log("表单验证失败:", error)
  }
}

// 取消弹窗
const handleCancel = () => {
  modalVisible.value = false
  formRef.value?.resetFields()
}
</script>

<style lang="scss" scoped>
.grade-management-cp {
  .search-area {
    background: #f5f5f5;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 6px;
  }

  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }

  .table-wrapper {
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;

      .empty-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      .empty-text {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .empty-desc {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        margin-bottom: 16px;
      }

      .ant-btn {
        width: 100%;
      }
    }
  }
}
</style>
