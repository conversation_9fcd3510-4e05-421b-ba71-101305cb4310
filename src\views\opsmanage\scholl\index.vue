<template>
  <div class="school-management">
    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false" style="margin-bottom: 16px">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="学校名称" class="marginBot">
          <a-input
            v-model:value="searchForm.SchoolName"
            placeholder="请输入学校名称"
            style="width: 200px"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="云校" class="marginBot">
          <a-select
            v-model:value="searchForm.CloudSchoolId"
            placeholder="请选择云校"
            style="width: 200px"
            allow-clear
            show-search
            :filter-option="filterOption"
            :options="[...cloudSchoolOptions, { label: '所有云校', value: 0 }]"
          >
          </a-select>
        </a-form-item>

        <a-form-item class="marginBot">
          <a-button type="primary" html-type="submit"> 搜索 </a-button>
          <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <!-- 操作按钮区域 -->
    <a-card class="search-card" :bordered="false" style="margin-bottom: 16px">
      <div>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </div>
    </a-card>

    <!-- 表格 -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="pagination"
      :loading="loading"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'schoolType'">
          {{ getSchoolTypeLabel(record.type) }}
        </template>
        <template v-if="column.key === 'enable'">
          <a-badge
            :status="record.enable ? 'success' : 'error'"
            :text="record.enable ? '启用' : '禁用'"
          />
        </template>
        <template v-if="column.key === 'region'">
          {{ record.pname + "-" + record.cname + "-" + record.rname }}
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleDetail(record)">详情</a-button>
            <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">删除</a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="600px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="学校名称" name="name">
          <a-input v-model:value="formData.name" placeholder="请输入学校名称" />
        </a-form-item>

        <a-form-item label="所属云校" name="cloudSchoolId">
          <a-select
            v-model:value="formData.cloudSchoolId"
            placeholder="请选择所属云校"
            :options="cloudSchoolOptions"
          />
        </a-form-item>

        <a-form-item label="区域" name="region">
          <a-cascader
            v-model:value="formData.region"
            :options="regionOptions"
            placeholder="请选择区域（支持搜索）"
            :field-names="{ label: 'name', value: 'code', children: 'children' }"
            :load-data="loadRegionData"
            :show-search="{
              filter: regionSearchFilter,
              placeholder: '输入省市区名称进行搜索',
              limit: 50
            }"
            :change-on-select="true"
            allow-clear
            :dropdown-style="{ maxHeight: '300px', overflow: 'auto' }"
            expand-trigger="hover"
            @change="handleRegionChange"
          />
        </a-form-item>

        <a-form-item label="学校类型" name="type">
          <a-select
            v-model:value="formData.type"
            placeholder="请选择学校类型"
            :options="schoolMenu"
          />
        </a-form-item>

        <a-form-item label="启用状态" name="enable">
          <a-radio-group v-model:value="formData.enable">
            <a-radio :value="true">启用</a-radio>
            <a-radio :value="false">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="学校详情"
      width="600px"
      :footer="null"
      @cancel="handleDetailCancel"
    >
      <div class="detail-content">
        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="学校名称">
            {{ detailData.name || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="学校类型">
            {{ getSchoolTypeLabel(detailData.type) || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="所属云校">
            {{ detailData.cloudSchoolName || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="学习官组长">
            {{ detailData.learningOfficerLeaderName || "-" }}
          </a-descriptions-item>
          <a-descriptions-item label="区域">
            {{ detailData.pname + "-" + detailData.cname + "-" + detailData.rname }}
          </a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-badge
              :status="detailData.enable ? 'success' : 'error'"
              :text="detailData.enable ? '启用' : '禁用'"
            />
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup name="School">
import {
  addSchoolApi,
  editSchoolApi,
  deleteSchoolApi,
  getSchoolListApi,
  getSchoolDetailApi,
  //   获取省
  getproviceApi,
  //   获取市
  getCityApi,
  //   获取区
  getRegionApi,
  //获取新增编辑弹窗里的所属云校数据
  getCloudSchoolDropdownListApi
} from "@/api/index.js"

// 响应式数据
const modalVisible = ref(false)
const detailVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const submitLoading = ref(false)
const loading = ref(false)
const currentRecord = ref(null)

// 表单数据
const formData = reactive({
  id: null,
  name: "",
  cloudSchoolId: null,
  region: [],
  type: null,
  enable: true
})

// 详情数据
const detailData = reactive({
  name: "",
  type: null,
  cloudSchoolName: "",
  learningOfficerLeaderName: "",
  pname: "",
  cname: "",
  rname: "",
  enable: true
})

// 学校类型选项 - 根据返回数据调整
const schoolMenu = [
  { label: "小学", value: 200 },
  { label: "初中", value: 100 },
  { label: "高中", value: 300 },
  { label: "初高中", value: 0 }
]

// 云校选项
const cloudSchoolOptions = ref([])

// 区域选项
const regionOptions = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `共 ${total} 条记录，第 ${range[0]}-${range[1]} 条`,
  onChange: (page, pageSize) => {
    pagination.current = page
    pagination.pageSize = pageSize
    getSchoolList()
  },
  onShowSizeChange: (_, size) => {
    pagination.current = 1
    pagination.pageSize = size
    getSchoolList()
  }
})

// 表格列配置
const columns = [
  {
    title: "id",
    dataIndex: "id",
    key: "id",
    width: 200
  },
  {
    title: "学校名称",
    dataIndex: "name",
    key: "name"
  },
  {
    title: "区域",
    dataIndex: "region",
    key: "region"
  },
  {
    title: "所属云校",
    dataIndex: "cloudSchoolName",
    key: "cloudSchoolName"
  },
  {
    title: "学校类型",
    dataIndex: "type",
    key: "schoolType"
  },
  {
    title: "启用状态",
    dataIndex: "enable",
    key: "enable"
  },
  {
    title: "操作",
    key: "action",
    width: 200
  }
]
/**
 * 过滤年级选项
 */
const filterOption = (input, option) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

/**
 * 区域搜索过滤函数
 */
const regionSearchFilter = (inputValue, path) => {
  // path 是一个数组，包含从根节点到当前节点的所有节点
  // 我们检查路径中的任何节点名称是否包含搜索关键词
  const searchValue = inputValue.toLowerCase().trim()

  if (!searchValue) return true

  return path.some(option => {
    if (!option.name) return false

    const optionName = option.name.toLowerCase()

    // 支持中文名称搜索
    return optionName.includes(searchValue)
  })
}

/**
 * 处理区域选择变化
 */
const handleRegionChange = async (value, selectedOptions) => {
  console.log("区域选择变化:", { value, selectedOptions })

  // 如果选择的不是完整的三级区域，需要继续加载子级数据
  if (value && value.length > 0 && value.length < 3) {
    const lastOption = selectedOptions[selectedOptions.length - 1]

    // 如果当前选项没有子数据，尝试加载
    if (!lastOption.children || lastOption.children.length === 0) {
      try {
        if (value.length === 1) {
          // 加载市级数据
          const res = await getCityApi(value[0])
          if (res.code === 200) {
            lastOption.children = res.data.map(item => ({
              name: item.cname,
              code: item.cid,
              isLeaf: false
            }))
          }
        } else if (value.length === 2) {
          // 加载区级数据
          const res = await getRegionApi(value[1])
          if (res.code === 200) {
            lastOption.children = res.data.map(item => ({
              name: item.rname,
              code: item.rid,
              isLeaf: true
            }))
          }
        }
      } catch (error) {
        console.error("加载子级数据失败:", error)
      }
    }
  }
}
// 表格数据
const dataSource = ref([])

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入学校名称", trigger: "blur" }],
  cloudSchoolId: [{ required: true, message: "请选择所属云校", trigger: "change" }],
  region: [{ required: true, message: "请选择区域", trigger: "change" }],
  type: [{ required: true, message: "请选择学校类型", trigger: "change" }],
  enable: [{ required: true, message: "请选择启用状态", trigger: "change" }]
}

// 表格查询参数
const searchForm = ref({
  SchoolName: undefined,
  CloudSchoolId: undefined
})
/**
 * 搜索
 */
const handleSearch = () => {
  pagination.current = 1
  getSchoolList()
}
// 重置表格数据
const handleReset = () => {
  searchForm.value = {
    SchoolName: undefined,
    CloudSchoolId: undefined
  }
  pagination.current = 1
  pagination.pageSize = 10
  getSchoolList()
}
/**
 * 获取学校列表数据
 */
const getSchoolList = async () => {
  if (loading.value) {
    return
  }

  try {
    loading.value = true
    const params = {
      PageIndex: pagination.current,
      PageSize: pagination.pageSize,
      ...searchForm.value
    }

    console.log("调用学校列表接口，参数:", params)
    const res = await getSchoolListApi(params)
    console.log("学校列表数据:", res)

    if (res.code === 200) {
      dataSource.value = res.data.items || []
      pagination.total = res.data.total || 0
    } else {
      message.error(res.msg || "获取数据失败")
    }
  } catch (error) {
    console.error("获取学校列表失败:", error)
  } finally {
    loading.value = false
  }
}

// 获取学校类型标签
const getSchoolTypeLabel = value => {
  const item = schoolMenu.find(item => item.value === value)
  return item ? item.label : value
}

/**
 * 获取云校选项数据
 */
const loadCloudSchoolOptions = async () => {
  try {
    const res = await getCloudSchoolDropdownListApi()
    if (res.code === 200) {
      cloudSchoolOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error("获取云校选项失败:", error)
  }
}

/**
 * 获取省级数据
 */
const loadProvinceData = async () => {
  try {
    const res = await getproviceApi()
    if (res.code === 200) {
      regionOptions.value = res.data.map(item => ({
        name: item.pname,
        code: item.pid,
        isLeaf: false // 标记非叶子节点，可以继续加载子级
      }))
    }
  } catch (error) {
    console.error("获取省级数据失败:", error)
  }
}

/**
 * 区域懒加载函数
 */
const loadRegionData = async selectedOptions => {
  const targetOption = selectedOptions[selectedOptions.length - 1]
  targetOption.loading = true

  try {
    if (selectedOptions.length === 1) {
      // 加载市级数据
      const res = await getCityApi(targetOption.code)
      if (res.code === 200) {
        targetOption.children = res.data.map(item => ({
          name: item.cname,
          code: item.cid,
          isLeaf: false // 标记非叶子节点
        }))
      }
    } else if (selectedOptions.length === 2) {
      // 加载区级数据
      const res = await getRegionApi(targetOption.code)
      if (res.code === 200) {
        targetOption.children = res.data.map(item => ({
          name: item.rname,
          code: item.rid,
          isLeaf: true // 标记为叶子节点
        }))
      }
    }
  } catch (error) {
    console.error("加载区域数据失败:", error)
  } finally {
    targetOption.loading = false
  }
}

/**
 * 编辑时回显区域数据 - 根据ID查询对应的省市区数据
 */
const loadRegionDataForEdit = async regionIds => {
  try {
    if (!regionIds || regionIds.length === 0) return

    const [pid, cid] = regionIds

    // 1. 加载省级数据
    await loadProvinceData()

    if (!pid) return

    // 2. 找到对应的省并加载市级数据
    const province = regionOptions.value.find(item => item.code == pid)
    if (province) {
      const cityRes = await getCityApi(pid)
      if (cityRes.code === 200) {
        province.children = cityRes.data.map(item => ({
          name: item.cname,
          code: item.cid,
          isLeaf: false
        }))

        if (!cid) return

        // 3. 找到对应的市并加载区级数据
        const city = province.children.find(item => item.code == cid)
        if (city) {
          const regionRes = await getRegionApi(cid)
          if (regionRes.code === 200) {
            city.children = regionRes.data.map(item => ({
              name: item.rname,
              code: item.rid,
              isLeaf: true
            }))
          }
        }
      }
    }

    console.log("编辑回显 - 区域数据加载完成:", regionOptions.value)
  } catch (error) {
    console.error("回显区域数据失败:", error)
  }
}

/**
 * 根据区域ID获取对应的名称
 */
const getRegionNames = regionIds => {
  if (!regionIds || regionIds.length !== 3) {
    return { pname: "", cname: "", rname: "" }
  }

  const [pid, cid, rid] = regionIds
  let pname = "",
    cname = "",
    rname = ""

  // 查找省名称
  const province = regionOptions.value.find(item => item.code == pid)
  if (province) {
    pname = province.name

    // 查找市名称
    if (province.children) {
      const city = province.children.find(item => item.code == cid)
      if (city) {
        cname = city.name

        // 查找区名称
        if (city.children) {
          const region = city.children.find(item => item.code == rid)
          if (region) {
            rname = region.name
          }
        }
      }
    }
  }

  return { pname, cname, rname }
}

// 新增
const handleAdd = () => {
  modalTitle.value = "新增"
  modalVisible.value = true
  currentRecord.value = null
  Object.assign(formData, {
    id: null,
    name: "",
    cloudSchoolId: null,
    region: [],
    type: null,
    enable: true
  })
}

// 编辑
const handleEdit = async record => {
  modalTitle.value = "编辑"
  modalVisible.value = true
  currentRecord.value = record

  // 构建区域数组
  const regionArray = []
  if (record.pid) regionArray.push(record.pid)
  if (record.cid) regionArray.push(record.cid)
  if (record.rid) regionArray.push(record.rid)
  //   测试数据???
  //   regionArray = [33, 33001, 33001002]

  Object.assign(formData, {
    id: record.id,
    name: record.name,
    cloudSchoolId: record.cloudSchoolId,
    region: regionArray,
    type: record.type,
    enable: record.enable
  })

  // 加载编辑时的区域数据 - 传入完整的ID数组
  if (regionArray.length > 0) {
    await loadRegionDataForEdit(regionArray)
  } else {
    // 直接获取省级数据(一级数据)
    await loadProvinceData()
  }
}

// 监听弹窗状态
watch(modalVisible, async isOpen => {
  if (isOpen) {
    // 加载云校选项
    await loadCloudSchoolOptions()

    // 如果是新增，只加载省级数据
    if (!currentRecord.value) {
      await loadProvinceData()
    }
  } else {
    // 关闭弹窗时重置表单
    if (formRef.value) {
      formRef.value.resetFields()
    }
    // 清空区域数据
    regionOptions.value = []
  }
})

// 详情
const handleDetail = async record => {
  const res = await getSchoolDetailApi(record.id)
  if (res.code === 200) {
    Object.assign(detailData, res.data)
  }
  detailVisible.value = true
}

// 删除
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除学校 "${record.name}" 吗？`,
    async onOk() {
      try {
        // 这里可以调用删除接口
        const res = await deleteSchoolApi([record.id])
        if (res.code === 200) {
          message.success("删除成功")
          getSchoolList()
        } else {
          message.error(res.msg || "删除失败")
        }
      } catch (error) {
        console.error("删除失败:", error)
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  console.log("选中的区域ID数组:", formData.region)

  // 验证是否选择了完整的三级区域
  if (formData.region.length !== 3) {
    message.warning("请选择完整的省市区三级区域")
    return
  }

  try {
    await formRef.value.validate()
    submitLoading.value = true

    // 获取区域名称
    const { pname, cname, rname } = getRegionNames(formData.region)
    const [pid, cid, rid] = formData.region

    // 获取云校名称
    const cloudSchool = cloudSchoolOptions.value.find(item => item.value === formData.cloudSchoolId)
    const cloudSchoolName = cloudSchool ? cloudSchool.label : ""

    console.log("提交表单数据:", formData)
    console.log("区域名称:", { pname, cname, rname })

    // 编辑
    if (formData.id) {
      const params = {
        id: formData.id,
        // 学校名称
        name: formData.name,
        // 省Id
        pid: pid,
        // 省name
        pname: pname,
        // 市Id
        cid: cid,
        // 市name
        cname: cname,
        // 区id
        rid: rid,
        // 区name
        rname: rname,
        // 启用状态
        enable: formData.enable,
        // 学校类型
        type: formData.type,
        // 云校id
        cloudSchoolId: formData.cloudSchoolId,
        // 云校name
        cloudSchoolName: cloudSchoolName
      }

      console.log("编辑参数:", params)
      const res = await editSchoolApi(params)
      if (res.code === 200) {
        message.success("更新成功")
        modalVisible.value = false
        getSchoolList()
      } else {
        message.error(res.msg || "更新失败")
      }
    } else {
      // 新增
      const params = {
        name: formData.name,
        pid: pid,
        pname: pname,
        cid: cid,
        cname: cname,
        rid: rid,
        rname: rname,
        enable: formData.enable,
        type: formData.type,
        cloudSchoolId: formData.cloudSchoolId,
        cloudSchoolName: cloudSchoolName
      }

      console.log("新增参数:", params)
      const res = await addSchoolApi(params)
      if (res.code === 200) {
        message.success("新增成功")
        modalVisible.value = false
        getSchoolList()
      } else {
        message.error(res.msg || "新增失败")
      }
    }
    submitLoading.value = false
  } catch (error) {
    console.log("表单验证失败:", error)
    submitLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  modalVisible.value = false
  submitLoading.value = false
}

const handleDetailCancel = () => {
  detailVisible.value = false
}

// 初始化数据
onMounted(() => {
  getSchoolList()
  loadCloudSchoolOptions()
})
</script>

<style scoped lang="scss">
.school-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}

.detail-content {
  .ant-descriptions-item-label {
    font-weight: 500;
  }
}
</style>
