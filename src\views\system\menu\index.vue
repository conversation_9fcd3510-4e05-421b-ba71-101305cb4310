<template>
  <div class="menu-management">
    <a-card>
      <div class="table-operations">
        <a-button type="primary" @click="handleAdd({})"> 新增 </a-button>
      </div>

      <a-table
        :columns="columns"
        :data-source="menuList"
        :loading="loading"
        row-key="id"
        :pagination="false"
        :expand-row-by-click="false"
        :child-row-key="'children'"
        :indent-size="20"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'icon'">
            <component :is="getIconComponent(record.icon)" v-if="record.icon" />
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <!-- 二级菜单没有新增按钮，因为菜单最多只有两级 -->
              <!-- <a-button v-if="!record.parentId" type="link" size="small" @click="handleAdd(record)"
                >新增</a-button
              > -->
              <a-button type="link" size="small" @click="handleAdd(record)">新增</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" size="small" danger @click="handleDelete(record)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="菜单名称" name="title">
          <a-input v-model:value="formData.title" placeholder="请输入菜单名称" />
        </a-form-item>

        <a-form-item label="路由路径" name="path">
          <a-input v-model:value="formData.path" placeholder="请输入路由路径" />
        </a-form-item>
        <a-form-item label="菜单图标" name="icon">
          <a-input
            v-model:value="formData.icon"
            placeholder="请选择菜单图标"
            readonly
            @click="handleSelectIcon"
            style="cursor: pointer"
          >
            <template #suffix>
              <component :is="getIconComponent(formData.icon)" v-if="formData.icon" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="父级菜单" name="parentId">
          <a-tree-select
            v-model:value="formData.parentId"
            :tree-data="parentMenuOptions"
            placeholder="请选择父级菜单,不选择或者选择空则默认添加为顶级菜单"
            allow-clear
            tree-default-expand-all
            :disabled="false"
            :field-names="{ children: 'children', label: 'title', value: 'id' }"
          />
        </a-form-item>

        <a-form-item label="排序" name="sort">
          <a-input-number
            v-model:value="formData.sort"
            placeholder="请输入排序值"
            :min="0"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 选择菜单图标弹窗 -->
    <IconSelector
      v-model="iconSelectorVisible"
      :default-icon="selectedIcon"
      @confirm="handleIconConfirm"
    />
  </div>
</template>

<script setup name="Menu">
import {
  addMenuApi,
  editMenuApi,
  deleteMenuApi,
  getMenuDetailApi,
  getMenuTreeApi
} from "@/api/index.js"

import {
  GroupOutlined,
  MehOutlined,
  // 搜索图标
  SearchOutlined,
  // 方向性图标
  DownOutlined,
  UpOutlined,
  LeftOutlined,
  RightOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  LeftCircleOutlined,
  RightCircleOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
  ForwardOutlined,
  BackwardOutlined,
  EnterOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  LoginOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,

  // 提示建议性图标
  QuestionOutlined,
  QuestionCircleOutlined,
  PlusOutlined,
  PlusCircleOutlined,
  PauseOutlined,
  PauseCircleOutlined,
  MinusOutlined,
  MinusCircleOutlined,
  InfoOutlined,
  InfoCircleOutlined,
  ExclamationOutlined,
  ExclamationCircleOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined,

  // 编辑类图标
  EditOutlined,
  FormOutlined,
  CopyOutlined,
  DeleteOutlined,
  SnippetsOutlined,
  HighlightOutlined,
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  RedoOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,

  // 数据类图标
  AreaChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  DotChartOutlined,
  LineChartOutlined,
  RadarChartOutlined,
  FallOutlined,
  RiseOutlined,
  StockOutlined,
  FundOutlined,
  SlidersOutlined,

  // 品牌和标识
  AndroidOutlined,
  AppleOutlined,
  WindowsOutlined,
  ChromeOutlined,
  GithubOutlined,
  WechatOutlined,
  AlipayOutlined,
  TaobaoOutlined,
  QqOutlined,
  DingdingOutlined,
  AntDesignOutlined,
  GoogleOutlined,
  FacebookOutlined,
  LinkedinOutlined,
  InstagramOutlined,
  SkypeOutlined,
  SlackOutlined,
  ZhihuOutlined,

  // 应用类图标
  HomeOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  DashboardOutlined,
  AppstoreOutlined,
  MenuOutlined,
  BarsOutlined,
  MailOutlined,
  BellOutlined,
  CalendarOutlined,
  FileOutlined,
  FolderOutlined,
  PictureOutlined,
  CameraOutlined,
  VideoCameraOutlined,
  MobileOutlined,
  TabletOutlined,
  LaptopOutlined,
  DesktopOutlined,
  PrinterOutlined,
  ScanOutlined,
  CloudOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  WifiOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  SafetyOutlined,
  BankOutlined,
  ShopOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  CreditCardOutlined,
  WalletOutlined,
  GiftOutlined,
  TrophyOutlined,
  CrownOutlined,
  HeartOutlined,
  StarOutlined,
  LikeOutlined,
  SmileOutlined,
  FireOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BookOutlined,
  ReadOutlined,
  BulbOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  BuildOutlined,
  BugOutlined,
  CodeOutlined,
  ApiOutlined,
  LinkOutlined,
  ShareAltOutlined,
  SendOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  SyncOutlined,
  ReloadOutlined,
  LoadingOutlined,
  PoweroffOutlined,
  HistoryOutlined,
  ScheduleOutlined,
  CarryOutOutlined,
  AuditOutlined,
  ProfileOutlined,
  ProjectOutlined,
  SolutionOutlined,
  TableOutlined,
  LayoutOutlined,
  FilterOutlined,
  TagOutlined,
  TagsOutlined,
  FlagOutlined,
  CompassOutlined,
  EnvironmentOutlined,
  AimOutlined,
  PushpinOutlined
} from "@ant-design/icons-vue"
// 图标组件映射
const iconComponents = {
  GroupOutlined,
  MehOutlined,
  // 搜索图标
  SearchOutlined,
  // 方向性图标
  DownOutlined,
  UpOutlined,
  LeftOutlined,
  RightOutlined,
  CaretUpOutlined,
  CaretDownOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  UpCircleOutlined,
  DownCircleOutlined,
  LeftCircleOutlined,
  RightCircleOutlined,
  DoubleRightOutlined,
  DoubleLeftOutlined,
  ForwardOutlined,
  BackwardOutlined,
  EnterOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  LoginOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,

  // 提示建议性图标
  QuestionOutlined,
  QuestionCircleOutlined,
  PlusOutlined,
  PlusCircleOutlined,
  PauseOutlined,
  PauseCircleOutlined,
  MinusOutlined,
  MinusCircleOutlined,
  InfoOutlined,
  InfoCircleOutlined,
  ExclamationOutlined,
  ExclamationCircleOutlined,
  CloseOutlined,
  CloseCircleOutlined,
  CheckOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  StopOutlined,

  // 编辑类图标
  EditOutlined,
  FormOutlined,
  CopyOutlined,
  DeleteOutlined,
  SnippetsOutlined,
  HighlightOutlined,
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  RedoOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  OrderedListOutlined,
  UnorderedListOutlined,

  // 数据类图标
  AreaChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  DotChartOutlined,
  LineChartOutlined,
  RadarChartOutlined,
  FallOutlined,
  RiseOutlined,
  StockOutlined,
  FundOutlined,
  SlidersOutlined,

  // 品牌和标识
  AndroidOutlined,
  AppleOutlined,
  WindowsOutlined,
  ChromeOutlined,
  GithubOutlined,
  WechatOutlined,
  AlipayOutlined,
  TaobaoOutlined,
  QqOutlined,
  DingdingOutlined,
  AntDesignOutlined,
  GoogleOutlined,
  FacebookOutlined,
  LinkedinOutlined,
  InstagramOutlined,
  SkypeOutlined,
  SlackOutlined,
  ZhihuOutlined,

  // 应用类图标
  HomeOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  DashboardOutlined,
  AppstoreOutlined,
  MenuOutlined,
  BarsOutlined,
  MailOutlined,
  BellOutlined,
  CalendarOutlined,
  FileOutlined,
  FolderOutlined,
  PictureOutlined,
  CameraOutlined,
  VideoCameraOutlined,
  MobileOutlined,
  TabletOutlined,
  LaptopOutlined,
  DesktopOutlined,
  PrinterOutlined,
  ScanOutlined,
  CloudOutlined,
  DatabaseOutlined,
  GlobalOutlined,
  WifiOutlined,
  LockOutlined,
  UnlockOutlined,
  KeyOutlined,
  SafetyOutlined,
  BankOutlined,
  ShopOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  CreditCardOutlined,
  WalletOutlined,
  GiftOutlined,
  TrophyOutlined,
  CrownOutlined,
  HeartOutlined,
  StarOutlined,
  LikeOutlined,
  SmileOutlined,
  FireOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BookOutlined,
  ReadOutlined,
  BulbOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  BuildOutlined,
  BugOutlined,
  CodeOutlined,
  ApiOutlined,
  LinkOutlined,
  ShareAltOutlined,
  SendOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  SyncOutlined,
  ReloadOutlined,
  LoadingOutlined,
  PoweroffOutlined,
  HistoryOutlined,
  ScheduleOutlined,
  CarryOutOutlined,
  AuditOutlined,
  ProfileOutlined,
  ProjectOutlined,
  SolutionOutlined,
  TableOutlined,
  LayoutOutlined,
  FilterOutlined,
  TagOutlined,
  TagsOutlined,
  FlagOutlined,
  CompassOutlined,
  EnvironmentOutlined,
  AimOutlined,
  PushpinOutlined
}
const loading = ref(false)
const modalVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const submitLoading = ref(false)
const currentRecord = ref(null)
// 图标选择器相关
const iconSelectorVisible = ref(false)
const selectedIcon = ref("")

// 表单数据
const formData = reactive({
  id: null,
  title: "",
  path: "",
  icon: "",
  parentId: "",
  sort: 0
})

// 表格列配置
const columns = [
  { title: "菜单名称", dataIndex: "title", key: "title" },
  { title: "路由路径", dataIndex: "path", key: "path" },
  { title: "图标", dataIndex: "icon", key: "icon" },
  { title: "排序", dataIndex: "sort", key: "sort" },
  { title: "操作", key: "action", width: 150 }
]

// 菜单列表数据（树形结构）
const menuList = ref([])
const parentMenuOptions = ref([])

// 表单验证规则
const rules = {
  title: [
    { required: true, message: "请输入菜单名称", trigger: "blur" },
    { min: 2, max: 50, message: "菜单名称长度为2-50个字符", trigger: "blur" }
  ],
  path: [
    { required: true, message: "请输入路由路径", trigger: "blur" },
    { pattern: /^\//, message: "路由路径必须以/开头", trigger: "blur" }
  ],
  icon: [{ required: true, message: "请选择菜单图标", trigger: "change" }],
  parentId: [{ required: false, message: "请选择父级菜单", trigger: "change" }]
}

// 获取图标组件
const getIconComponent = iconName => {
  return iconComponents[iconName] || null
}

// 新增
const handleAdd = (record = {}) => {
  console.log("新增子级菜单", record)

  modalTitle.value = "新增"
  modalVisible.value = true
  currentRecord.value = null
  Object.assign(formData, {
    // 数据id
    id: null,
    // 菜单名称
    title: "",
    // 路由路径
    path: "",
    // 菜单图标
    icon: "",
    // 父级菜单
    parentId: record.id,
    // 排序：暂定0
    sort: 0
  })
}

// 编辑
const handleEdit = record => {
  modalTitle.value = "编辑"
  modalVisible.value = true
  getMenuDetailApi(record.id).then(res => {
    if (res.code === 200) {
      currentRecord.value = res.data
      Object.assign(formData, res.data)
    }
  })
}

watch(
  () => modalVisible.value,
  val => {
    if (!val) {
      formRef.value.resetFields()
    }
  }
)

// 删除
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除菜单 "${record.title}" 吗？`,
    async onOk() {
      try {
        // 这里应该调用删除API
        deleteMenuApi(record.id).then(res => {
          if (res.code === 200) {
            message.success("删除成功")
            getMenuTree()
          }
        })
        // 刷新列表
      } catch (error) {
        console.log(error)
        // message.error("删除失败")
      }
    }
  })
}

// 选择图标
const handleSelectIcon = () => {
  selectedIcon.value = formData.icon
  iconSelectorVisible.value = true
}

// 确认选中图标
const handleIconConfirm = iconName => {
  formData.icon = iconName
  iconSelectorVisible.value = false
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    let componentName
    // 添加的是顶级菜单
    if (!formData.parentId) {
      componentName = "Layout"
    } else {
      // 否则就用路径名作为组件名
      componentName = formData.path.replace("/", "")
    }
    // 编辑
    if (formData.id) {
      const newFormData = { ...formData, component: componentName }
      // 编辑
      editMenuApi(newFormData).then(res => {
        if (res.code === 200) {
          message.success("编辑成功")
          //  刷新列表
          getMenuTree()
        }
      })
    } else {
      // 新增
      const { id, ...formDataWithoutId } = formData // 解构出 id，并将剩余的部分赋给 formDataWithoutId
      const newFormData = {
        ...formDataWithoutId,
        component: componentName,
        parentId: formData.parentId ? formData.parentId : 0
      }

      console.log("删除id后的提交数据", newFormData)
      addMenuApi(newFormData).then(res => {
        if (res.code === 200) {
          message.success("新增成功")
          // modalVisible.value = false
          //  刷新列表
          getMenuTree()
        } else {
          message.error(res.msg)
        }
      })
    }

    modalVisible.value = false
    // 刷新列表
  } catch (error) {
    console.log("表单验证失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  submitLoading.value = false
}
const getMenuTree = () => {
  getMenuTreeApi().then(res => {
    if (res.code === 200) {
      menuList.value = res.data
      // 获取父级菜单
      // parentMenuOptions.value = (res.data || []).map(i => {
      //   return {
      //     id: i.id,
      //     title: i.title
      //   }
      // })
      parentMenuOptions.value = [...res.data]
      parentMenuOptions.value.push({
        id: 0,
        title: "空"
      })
      console.log(parentMenuOptions.value)
    }
  })
}
// 组件挂载时获取数据
onMounted(() => {
  getMenuTree()
})
</script>

<style lang="scss" scoped>
.menu-management {
  .table-operations {
    margin-bottom: 16px;
  }
}
</style>
