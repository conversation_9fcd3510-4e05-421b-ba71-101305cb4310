<template>
  <div class="tasktype-management">
    <!-- 操作按钮区域 -->
    <div class="table-operations">
      <a-button type="primary" @click="handleAdd"> 新增 </a-button>
    </div>

    <!-- 表格 -->
    <!-- :row-selection="rowSelection" -->
    <a-table
      :columns="columns"
      :data-source="dataSource"
      :pagination="false"
      :loading="loading"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" size="small" @click="handleEdit(record)"> 编辑 </a-button>
            <a-button type="link" size="small" danger @click="handleDelete(record)">
              删除
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 新增/编辑弹窗 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalTitle"
      width="500px"
      :confirm-loading="submitLoading"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form ref="formRef" :model="formData" :rules="rules" layout="vertical">
        <a-form-item label="类型名称" name="typeName">
          <a-input v-model:value="formData.typeName" placeholder="请输入类型名称" />
        </a-form-item>

        <a-form-item label="类型枚举值" name="enumValue">
          <a-input v-model:value="formData.enumValue" placeholder="请输入类型枚举值" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup name="TaskType">
import {
  addTaskTypeApi,
  editTaskTypeApi,
  deleteTaskTypeApi,
  getTaskTypeListApi
} from "@/api/index.js"

// 响应式数据
const modalVisible = ref(false)
const modalTitle = ref("")
const formRef = ref()
const loading = ref(false)
const submitLoading = ref(false)
const deleteLoading = ref(false)

// 表单数据
const formData = reactive({
  id: null,
  typeName: "",
  enumValue: ""
})

// 表格列配置
const columns = [
  {
    title: "ID",
    dataIndex: "id",
    key: "id",
    width: 80
  },
  {
    title: "类型名称",
    dataIndex: "typeName",
    key: "typeName"
  },
  {
    title: "类型枚举值",
    dataIndex: "enumValue",
    key: "enumValue"
  },
  {
    title: "操作",
    key: "action",
    width: 150
  }
]

// 表格数据
const dataSource = ref([])

// 表单验证规则
const rules = {
  typeName: [{ required: true, message: "请输入类型名称", trigger: "blur" }],
  enumValue: [{ required: true, message: "请输入类型枚举值", trigger: "blur" }]
}

/**
 * 获取任务类型列表数据
 */
const getTaskTypeList = async () => {
  // 如果正在加载中，直接返回，避免重复调用
  if (loading.value) {
    console.log("正在加载中，跳过重复调用")
    return
  }

  try {
    loading.value = true
    console.log("调用任务类型列表接口")

    const res = await getTaskTypeListApi()
    console.log("任务类型列表数据:", res)

    if (res && Array.isArray(res)) {
      // 直接返回数组的情况
      dataSource.value = res
    } else if (res && res.code === 200) {
      // 返回包装对象的情况
      dataSource.value = res.data || []
    } else {
      // 其他情况，使用空数组
      dataSource.value = []
      if (res && res.msg) {
        message.error(res.msg)
      }
    }
  } catch (error) {
    console.error("获取任务类型列表失败:", error)
    dataSource.value = []
  } finally {
    loading.value = false
  }
}

// 新增
const handleAdd = () => {
  modalTitle.value = "新增"
  modalVisible.value = true
  Object.assign(formData, {
    id: null,
    typeName: "",
    enumValue: ""
  })
}

// 编辑
const handleEdit = record => {
  modalTitle.value = "编辑"
  modalVisible.value = true
  Object.assign(formData, {
    id: record.id,
    typeName: record.typeName,
    enumValue: record.enumValue
  })
}

// 删除单条记录
const handleDelete = record => {
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除任务类型 "${record.typeName}" 吗？`,
    confirmLoading: deleteLoading.value,
    async onOk() {
      try {
        console.log("删除任务类型:", record.id)
        deleteLoading.value = true
        const res = await deleteTaskTypeApi(record.id)
        console.log("删除结果:", res)

        if (res && res.code === 200) {
          message.success("删除成功")
          // 刷新列表
          getTaskTypeList()
        } else {
          message.error(res?.msg || "删除失败")
        }
      } catch (error) {
        console.error("删除失败:", error)
      } finally {
        deleteLoading.value = false
      }
    }
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    submitLoading.value = true

    const params = {
      typeName: formData.typeName,
      enumValue: formData.enumValue
    }

    console.log("提交参数:", params)

    if (formData.id) {
      // 编辑 - 添加ID参数
      const editParams = {
        id: formData.id,
        ...params
      }
      console.log("编辑任务类型:", editParams)

      const res = await editTaskTypeApi(editParams)
      console.log("编辑结果:", res)

      if (res && res.code === 200) {
        message.success("更新成功")
        modalVisible.value = false
        // 刷新列表
        getTaskTypeList()
      } else {
        message.error(res?.msg || "更新失败")
      }
    } else {
      // 新增
      console.log("新增任务类型:", params)

      const res = await addTaskTypeApi(params)
      console.log("新增结果:", res)

      if (res && res.code === 200) {
        message.success("新增成功")
        modalVisible.value = false
        // 刷新列表
        getTaskTypeList()
      } else {
        message.error(res?.msg || "新增失败")
      }
    }
  } catch (error) {
    console.log("表单验证失败:", error)
  } finally {
    submitLoading.value = false
  }
}

// 取消
const handleCancel = () => {
  modalVisible.value = false
  // 重置提交loading状态
  submitLoading.value = false
}

// 组件挂载时获取数据
onMounted(() => {
  console.log("任务类型管理组件已挂载，开始获取数据")
  getTaskTypeList()
})
</script>

<style lang="scss" scoped>
.tasktype-management {
  .table-operations {
    margin-bottom: 16px;

    .ant-btn {
      margin-right: 8px;
    }
  }
}
</style>
