<template>
  <div class="error-page">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>
    <!-- 主要内容 -->
    <div class="error-content">
      <!-- 404 数字动画 -->
      <div class="error-number">
        <div class="number-item">
          <span class="number">4</span>
          <div class="number-shadow">4</div>
        </div>
        <div class="number-item middle">
          <span class="number">0</span>
          <div class="number-shadow">0</div>
          <!-- 眼睛动画 -->
          <div class="eyes">
            <div class="eye left-eye">
              <div class="eyeball"></div>
            </div>
            <div class="eye right-eye">
              <div class="eyeball"></div>
            </div>
          </div>
        </div>
        <div class="number-item">
          <span class="number">4</span>
          <div class="number-shadow">4</div>
        </div>
      </div>
      <!-- 错误信息 -->
      <div class="error-info">
        <h2 class="error-title">页面走丢了</h2>
        <p class="error-description">抱歉，您访问的页面不存在或已被移除</p>
      </div>
      <!-- 操作按钮 -->
      <div class="error-actions">
        <a-button type="primary" size="large" @click="goHome" class="action-btn primary-btn">
          <!-- <home-outlined /> -->
          返回上一页
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { HomeOutlined } from "@ant-design/icons-vue"
import { useRouter } from "vue-router"
const router = useRouter()

// 返回首页
const goHome = () => {
  router.go(-1)
}

// 眼睛跟随鼠标
onMounted(() => {
  const eyeballs = document.querySelectorAll(".eyeball")

  document.addEventListener("mousemove", e => {
    eyeballs.forEach(eyeball => {
      const eye = eyeball.parentElement
      const eyeRect = eye.getBoundingClientRect()
      const eyeCenterX = eyeRect.left + eyeRect.width / 2
      const eyeCenterY = eyeRect.top + eyeRect.height / 2

      const angle = Math.atan2(e.clientY - eyeCenterY, e.clientX - eyeCenterX)
      const distance = Math.min(8, Math.hypot(e.clientX - eyeCenterX, e.clientY - eyeCenterY) / 10)

      const x = Math.cos(angle) * distance
      const y = Math.sin(angle) * distance

      eyeball.style.transform = `translate(${x}px, ${y}px)`
    })
  })
})
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

// 背景装饰
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
  }

  &.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
  }

  &.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
  }

  &.shape-5 {
    width: 40px;
    height: 40px;
    top: 50%;
    left: 5%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 主要内容
.error-content {
  text-align: center;
  z-index: 10;
  position: relative;
  max-width: 600px;
  width: 100%;
}

// 404 数字
.error-number {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  gap: 20px;
}

.number-item {
  position: relative;
  display: inline-block;

  &.middle {
    position: relative;
  }
}

.number {
  font-size: 120px;
  font-weight: 900;
  color: #fff;
  text-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  display: block;
  animation: bounce 2s ease-in-out infinite;

  @media (max-width: 768px) {
    font-size: 80px;
  }
}

.number-shadow {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 120px;
  font-weight: 900;
  color: rgba(0, 0, 0, 0.2);
  z-index: -1;

  @media (max-width: 768px) {
    font-size: 80px;
    top: 6px;
    left: 6px;
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// 眼睛动画
.eyes {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 20px;
}

.eye {
  width: 30px;
  height: 30px;
  background: #fff;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.eyeball {
  width: 12px;
  height: 12px;
  background: #333;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.1s ease;
}

// 错误信息
.error-info {
  margin-bottom: 40px;

  .error-title {
    font-size: 36px;
    color: #fff;
    margin-bottom: 16px;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);

    @media (max-width: 768px) {
      font-size: 28px;
    }
  }

  .error-description {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 12px;
    line-height: 1.6;
  }

  .error-suggestion {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
  }
}

// 操作按钮
.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.action-btn {
  height: 48px;
  padding: 0 24px;
  border-radius: 24px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  backdrop-filter: blur(10px);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
  }

  &.primary-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border-color: transparent;

    &:hover {
      background: linear-gradient(45deg, #ff5252, #e55100);
      border-color: transparent;
    }
  }

  .anticon {
    margin-right: 8px;
  }
}

// 帮助链接
.help-links {
  .links {
    display: flex;
    justify-content: center;
    gap: 32px;
    flex-wrap: wrap;
  }

  .help-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;

    &:hover {
      color: #fff;
      transform: translateY(-2px);
    }
  }
}

// 底部装饰波浪
.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100px;
  overflow: hidden;
}

.wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1000px 1000px 0 0;
  animation: wave 10s linear infinite;

  &.wave1 {
    animation-delay: 0s;
    opacity: 0.3;
  }

  &.wave2 {
    animation-delay: -2s;
    opacity: 0.2;
  }

  &.wave3 {
    animation-delay: -4s;
    opacity: 0.1;
  }
}

@keyframes wave {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-page {
    padding: 20px 16px;
  }

  .error-number {
    gap: 10px;
    margin-bottom: 30px;
  }

  .eyes {
    gap: 15px;
  }

  .eye {
    width: 20px;
    height: 20px;
  }

  .eyeball {
    width: 8px;
    height: 8px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;

    .action-btn {
      width: 200px;
    }
  }

  .help-links .links {
    flex-direction: column;
    gap: 16px;
  }
}

// Ant Design 组件样式覆盖
:deep(.ant-divider) {
  border-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}
</style>
